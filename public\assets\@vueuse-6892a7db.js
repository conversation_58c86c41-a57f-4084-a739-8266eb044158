import{g as j,b as B,r as P,u as L,d as F,e as V,n as M,w,f as W}from"./@vue-ac51f1de.js";var A;const E=typeof window<"u",z=e=>typeof e<"u",re=e=>typeof e=="boolean",J=e=>typeof e=="function",ae=e=>typeof e=="number",U=e=>typeof e=="string",D=()=>{};E&&((A=window==null?void 0:window.navigator)!=null&&A.userAgent)&&/iP(ad|hone|od)/.test(window.navigator.userAgent);function R(e){return typeof e=="function"?e():L(e)}function G(e){return e}function S(e){return j()?(B(e),!0):!1}function H(e,t=!0){F()?V(e):t?e():M(e)}function ie(e,t,o={}){const{immediate:n=!0}=o,a=P(!1);let i=null;function u(){i&&(clearTimeout(i),i=null)}function l(){a.value=!1,u()}function d(...p){u(),a.value=!0,i=setTimeout(()=>{a.value=!1,i=null,e(...p)},R(t))}return n&&(a.value=!0,E&&d()),S(l),{isPending:a,start:d,stop:l}}function _(e){var t;const o=R(e);return(t=o==null?void 0:o.$el)!=null?t:o}const N=E?window:void 0;function y(...e){let t,o,n,a;if(U(e[0])||Array.isArray(e[0])?([o,n,a]=e,t=N):[t,o,n,a]=e,!t)return D;Array.isArray(o)||(o=[o]),Array.isArray(n)||(n=[n]);const i=[],u=()=>{i.forEach(c=>c()),i.length=0},l=(c,v,f)=>(c.addEventListener(v,f,a),()=>c.removeEventListener(v,f,a)),d=w(()=>_(t),c=>{u(),c&&i.push(...o.flatMap(v=>n.map(f=>l(c,v,f))))},{immediate:!0,flush:"post"}),p=()=>{d(),u()};return S(p),p}function ue(e,t,o={}){const{window:n=N,ignore:a=[],capture:i=!0,detectIframe:u=!1}=o;if(!n)return;let l=!0,d;const p=s=>a.some(r=>{if(typeof r=="string")return Array.from(n.document.querySelectorAll(r)).some(m=>m===s.target||s.composedPath().includes(m));{const m=_(r);return m&&(s.target===m||s.composedPath().includes(m))}}),c=s=>{n.clearTimeout(d);const r=_(e);if(!(!r||r===s.target||s.composedPath().includes(r))){if(s.detail===0&&(l=!p(s)),!l){l=!0;return}t(s)}},v=[y(n,"click",c,{passive:!0,capture:i}),y(n,"pointerdown",s=>{const r=_(e);r&&(l=!s.composedPath().includes(r)&&!p(s))},{passive:!0}),y(n,"pointerup",s=>{if(s.button===0){const r=s.composedPath();s.composedPath=()=>r,d=n.setTimeout(()=>c(s),50)}},{passive:!0}),u&&y(n,"blur",s=>{var r;const m=_(e);((r=n.document.activeElement)==null?void 0:r.tagName)==="IFRAME"&&!(m!=null&&m.contains(n.document.activeElement))&&t(s)})].filter(Boolean);return()=>v.forEach(s=>s())}function q(e,t=!1){const o=P(),n=()=>o.value=Boolean(e());return n(),H(n,t),o}function K(e){return JSON.parse(JSON.stringify(e))}const I=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},h="__vueuse_ssr_handlers__";I[h]=I[h]||{};I[h];var C=Object.getOwnPropertySymbols,X=Object.prototype.hasOwnProperty,Y=Object.prototype.propertyIsEnumerable,Z=(e,t)=>{var o={};for(var n in e)X.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(e!=null&&C)for(var n of C(e))t.indexOf(n)<0&&Y.call(e,n)&&(o[n]=e[n]);return o};function le(e,t,o={}){const n=o,{window:a=N}=n,i=Z(n,["window"]);let u;const l=q(()=>a&&"ResizeObserver"in a),d=()=>{u&&(u.disconnect(),u=void 0)},p=w(()=>_(e),v=>{d(),l.value&&a&&v&&(u=new ResizeObserver(t),u.observe(v,i))},{immediate:!0,flush:"post"}),c=()=>{d(),p()};return S(c),{isSupported:l,stop:c}}var x;(function(e){e.UP="UP",e.RIGHT="RIGHT",e.DOWN="DOWN",e.LEFT="LEFT",e.NONE="NONE"})(x||(x={}));var k=Object.defineProperty,Q=Object.getOwnPropertySymbols,ee=Object.prototype.hasOwnProperty,te=Object.prototype.propertyIsEnumerable,$=(e,t,o)=>t in e?k(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,ne=(e,t)=>{for(var o in t||(t={}))ee.call(t,o)&&$(e,o,t[o]);if(Q)for(var o of Q(t))te.call(t,o)&&$(e,o,t[o]);return e};const oe={easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]};ne({linear:G},oe);function ce(e,t,o,n={}){var a,i,u;const{clone:l=!1,passive:d=!1,eventName:p,deep:c=!1,defaultValue:v}=n,f=F(),s=o||(f==null?void 0:f.emit)||((a=f==null?void 0:f.$emit)==null?void 0:a.bind(f))||((u=(i=f==null?void 0:f.proxy)==null?void 0:i.$emit)==null?void 0:u.bind(f==null?void 0:f.proxy));let r=p;t||(t="modelValue"),r=p||r||`update:${t.toString()}`;const m=O=>l?J(l)?l(O):K(O):O,T=()=>z(e[t])?m(e[t]):v;if(d){const O=T(),g=P(O);return w(()=>e[t],b=>g.value=m(b)),w(g,b=>{(b!==e[t]||c)&&s(r,b)},{deep:c}),g}else return W({get(){return T()},set(O){s(r,O)}})}export{ae as a,re as b,y as c,_ as d,ce as e,ie as f,E as i,ue as o,S as t,le as u};
