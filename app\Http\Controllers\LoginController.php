<?php

namespace App\Http\Controllers;

use App\Models\SqlModel;
use Illuminate\Support\Facades\Validator;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;

class LoginController extends Controller
{
    //登录接口
    public function doLogin(Request $request)
    {
        //dd($request->cookies);
        $validator = Validator::make($request->all(), [
            'username' => 'required|max:16|min:2',
            'password' => 'required',
        ], [
            'username.required' => "请输入用户名",
            'username.max' => "用户名最大长度不能超过16个字符",
            'username.min' => "用户名最小长度不能低于5个字符",
            'password.required' => "请输入密码",
        ]);
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        $db = new User();
        $res_sql = $db->selectFromWhere($request->all()['username'], $request->all()['password']);
        if (!$res_sql) {
            $res['msg'] = 1;
            $res['infor'] =  "用户名密码不对！";
            return $res;
        } else {
            SqlModel::check_bak();
            $res['msg'] = 2;
            $res['infor'] = "登录成功！";
            $res['list'] = $res_sql;
            $d = Auth::guard('qian')->login($res_sql, $remember = true);
            return $res;
        }
    }
    //登录接口
    public function checkLogin(Request $request)
    {
        $res['message'] = "状态正常";
        $res['errorCode'] = 0;
        return $res;
    }
    //登录接口
    public function logOut()
    {
        Auth::guard('qian')->logout();
        $res['msg'] = 2;
        $res['infor'] = "退出成功！";
        $res['list'] = [];
        return $res;
    }
}
