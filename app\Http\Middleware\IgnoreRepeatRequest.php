<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redis;

class IgnoreRepeatRequest
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // if (Cache::has($key)) {
        //     // $res['msg'] = 1;
        //     // $res['infor'] = "禁止重复点击！";
        //     // return response()->json($res);
        //     return abort(429);
        // }
        // Cache::put($key, ", 1);
        //return $next($request);
        $key = (md5($request->route()->uri() . Auth::guard('qian')->user()->id . md5(json_encode($request->all()))));
        if (Redis::setnx($key, 1)) {
            Redis::expire($key, $set["max_time"] ?? 60);
        } else {
            $res['msg'] = 1;
            $res['infor'] = "禁止重复点击！";
            return response()->json($res);
        }
        $response = $next($request);
        Redis::del($key);
        return $response;
        // if (Cache::has($key)) {
        //     // $res['msg'] = 1;
        //     // $res['infor'] = "禁止重复点击！";
        //     // return response()->json($res);
        //     return abort(429);
        // }
        // Cache::put($key, ", 1);
        //return $next($request);
    }
}
