<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class def
{
    public string $label = "";
}
class SqlModel extends Model
{
    use HasFactory;
    /**
     * 根据oldmanId获取采集照片
     */
    public static function selectCjFromOldmanId($id)
    {
        //dd($id);
        $res = DB::select("(SELECT path,f_type,father_id
        FROM lk_service_files f1
        INNER JOIN (
        SELECT DISTINCT(id) id
        FROM lk_service_files
        WHERE tablename='lk_service_oldman' AND father_id=?
        ORDER BY id DESC) AS f2 ON f2.id = f1.id
        WHERE f1.tablename='lk_service_oldman' AND f1.father_id=?
        GROUP BY f1.f_type)", [$id, $id]);
        return $res;
    }

    /**
     * 根据orderId获取服务照片/视频
     */
    public static function selectFwFromOrdersId($id)
    {
        //dd($id);
        // $res = DB::select("(SELECT path,f_type,father_id,t,item
        // FROM lk_service_files f1
        // INNER JOIN (
        // SELECT DISTINCT(id) id
        // FROM lk_service_files
        // WHERE tablename='lk_service_orders' AND father_id=?
        // ORDER BY id DESC) AS f2 ON f2.id = f1.id
        // WHERE f1.tablename='lk_service_orders' AND f1.father_id=?
        // GROUP BY f1.f_type,f1.item,f1.t)", [$id, $id]);
        $res = DB::select("SELECT f1.path, f1.f_type, f1.father_id, f1.t, f1.item
        FROM lk_service_files f1
        WHERE f1.tablename = 'lk_service_orders'
        AND f1.father_id = ?
        AND EXISTS (
        SELECT 1
        FROM lk_service_files f2
        WHERE f2.tablename = 'lk_service_orders'
        AND f2.father_id = ?
        AND f2.id = f1.id
        )", [$id, $id]);

        return $res;
    }
    /**
     * 根据住院护理orderId获取服务照片/视频
     */
    public static function selectFwFromBeianOrdersId($id)
    {
        //dd($id);
        // $res = DB::select("(SELECT path,f_type,father_id,t,item
        // FROM lk_service_files f1
        // INNER JOIN (
        // SELECT DISTINCT(id) id
        // FROM lk_service_files
        // WHERE tablename='lk_service_orders' AND father_id=?
        // ORDER BY id DESC) AS f2 ON f2.id = f1.id
        // WHERE f1.tablename='lk_service_orders' AND f1.father_id=?
        // GROUP BY f1.f_type,f1.item,f1.t)", [$id, $id]);
        $res = DB::select("SELECT f1.path, f1.f_type, f1.father_id, f1.t, f1.item
        FROM lk_service_files f1
        WHERE f1.tablename = 'pgm_beian_orders'
        AND f1.father_id = ?
        AND EXISTS (
        SELECT 1
        FROM lk_service_files f2
        WHERE f2.tablename = 'pgm_beian_orders'
        AND f2.father_id = ?
        AND f2.id = f1.id
        )", [$id, $id]);

        return $res;
    }

    /**
     * 根据orderId获取服务项目
     */
    public static function selectOrderItemFromOrdersId( $sub_items,$id)
    {
// 1. 获取订单的 start_time
$dbTime = LkServiceOrders::select('start_time')
    ->where('id', $id)
    ->first();
$startTime = $dbTime->start_time;

// 2. 计算当前月份的月初和月末时间戳
$monthStart = strtotime(date('Y-m-01 00:00:00', $startTime)); // 当月第一天 00:00:00
$monthEnd = strtotime(date('Y-m-t 23:59:59', $startTime));    // 当月最后一天 23:59:59

        // DB::connection()->enableQueryLog();
        //指定man_state下的服务项目列表
        // $orders_item_manstate = Cache::rememberForever("orders_item_manstate_" . $man_state, function () use ($man_state, $id) {
        //     return DB::table("lk_service_items")
        //         ->leftJoin('lk_service_orders_items', function ($join) use ($id) {
        //             $join->on('lk_service_items.id', '=', 'lk_service_orders_items.item_id')
        //                 ->on('lk_service_orders_items.order_id', '=', $id);
        //         })
        //         ->whereRaw("(man_state=" . $man_state . " OR man_state=0) AND deleted=0")
        //         ->orderByRaw("man_state ASC,sort ASC")
        //         ->get();
        // });
        //1111
        // $orders_item_manstate = DB::table("lk_service_items")
        //     ->selectRaw("lk_service_items.*,lk_service_orders_items.item_values_str")
        //     ->leftJoin('lk_service_orders_items', function ($join) use ($id) {
        //         $join->on('lk_service_items.id', '=', 'lk_service_orders_items.item_id')
        //             ->where('lk_service_orders_items.order_id', '=', $id);
        //     })
        //     ->whereRaw("(lk_service_items.man_state=" . $man_state . " OR lk_service_items.man_state=0) AND lk_service_items.deleted=0 and lk_service_items.versions=" . env('VERSIONS'))
        //     ->orderByRaw("lk_service_items.man_state ASC,lk_service_items.sort ASC")
        //     ->get();
        //2222
        // DB::connection()->enableQueryLog();
        $sub_items_array = explode(',', $sub_items); // 将字符串转换为数组

$inClause = implode(',', array_fill(0, count($sub_items_array), '?')); // 生成占位符

$orders_item_manstate = DB::select("
    SELECT
        om.man_state, 0 as use_plan,
        om.id, si.need_number, si.need_pic_count, si.id itid, si.str_value, oii.item_values_str,
        si.id AS item_id,
        si.period_nums AS period_nums,
        si.menu_type, si.name,
        COALESCE(COUNT(oi.item_id), 0) AS done_times
    FROM lk_service_oldman om
    JOIN lk_service_orders o ON om.id = o.oldman_id
    JOIN lk_service_items si ON si.id IN ($inClause)
    LEFT JOIN lk_service_orders_items oi ON o.id = oi.order_id AND si.id = oi.item_id AND o.id != ?
    LEFT JOIN lk_service_orders_items oii ON oii.order_id = ? AND oii.item_id = si.id
    WHERE om.id = (SELECT oldman_id FROM lk_service_orders WHERE id = ?)
        AND o.start_time > ?  -- 动态传入月初时间戳
        AND o.start_time < ?  -- 动态传入月末时间戳
    GROUP BY si.id
    ORDER BY si.id;",
    array_merge($sub_items_array, [$id, $id, $id, $monthStart, $monthEnd])
);
        //$res_array = collect($orders_item_manstate)->toArray();
        // dd(DB::getQueryLog());
        // dd(233,$sub_items_array,$orders_item_manstate);
        //如果使用服务计划
        if (env("USE_ITEMS_PLAN") == true) {
            //如果查到了服务项目
            if (count($orders_item_manstate) > 0) {
                $man_state_orderItems = SqlModel::getOrderItemsPlanByManState($orders_item_manstate[0]->man_state);
                //如果今天的服务计划不存在
                if (count($man_state_orderItems) == 0) {
                    foreach ($orders_item_manstate as $key => $value) {
                        $value->can_show = false;
                        $value->use_plan = 1;
                    }
                } else {
                    foreach ($orders_item_manstate as $key => $value) {
                        $value->can_show = false;
                        $value->use_plan = 1;
                        foreach ($man_state_orderItems as $or_key => $or_value) {
                            //如果是is_all等于1，匹配所有老人;如果是is_all等于0，手动匹配是否有本老人
                            //dd($or_value->is_all, $value->id, explode(",", $or_value->oldman_ids));
                            if ($or_value->is_all == 1 || in_array($value->id, explode(",", $or_value->oldman_ids))) {
                                $items = explode(",", $or_value->item_ids);
                                //dd($value->itid, $items);
                                if (in_array($value->itid, $items)) {
                                    $value->can_show = true;
                                }
                            }
                        }
                        //dd($key, $value, $man_state_orderItems);
                    }
                    // dd(count($orders_item_manstate), $man_state_orderItems);
                }
            }
        }

        // $orders_item_manstate = DB::table('lk_service_items as i')
        //     ->leftJoin('lk_service_orders_items as oi', 'oi.item_id', '=', 'i.id')
        //     ->leftJoin('lk_service_orders as o', function ($join) use ($id) {
        //         $join->on('o.id', '=', 'oi.order_id')
        //             ->where('o.id', '=', $id);
        //     })
        //     ->where(function ($query) {
        //         $query->whereBetween(DB::raw('UNIX_TIMESTAMP(o.start_time)'), [
        //             strtotime(date('Y-m') . '-01 00:00:00'),
        //             strtotime(date('Y-m-t') . ' 23:59:59')
        //         ])->orWhereNull('o.id');
        //     })
        //     ->groupBy('i.id', 'i.name', 'i.period_nums')
        //     ->select('i.*', DB::raw('COUNT(oi.id) AS done_times'))
        //     ->get();
        //dd(DB::getQueryLog());
        //dd($orders_item_manstate);
        //服务订单下的服务照片
        // $orders_file = DB::table("lk_service_files")
        //     ->where("tablename", '=', 'lk_service_orders')
        //     ->where("father_id", '=', $id)
        //     ->get();
        //$res = DB::select("SELECT f_type,father_id,id,t FROM lk_service_files WHERE tablename='lk_service_orders' AND father_id=?", [$id]);
        $a_item = Auth::jiangweiArr($orders_item_manstate, "menu_type");
        // dd($orders_item_manstate,$a_item);
        $res['items'] = $a_item ? $a_item : new def();
        $res['all_items'] = $orders_item_manstate;
        //dd($orders_item_manstate);
        return $res;
    }
    /**
     * 每次登录，都应该检查一下备份情况
     */

    public static function check_bak()
    {

        $tablename = 'lk_service_oldman_' . date("Ym", strtotime('last month'));
        $con = DB::table("information_schema.TABLES")
            ->where("table_name", "=", $tablename)
            ->where("table_schema", "=", env("DB_DATABASE"))
            ->count();
        //dd($con);
        if ($con == 0) {
            //执行复制表
            $sql = "CREATE TABLE " . $tablename . " like lk_service_oldman";
            DB::unprepared($sql);
            //执行设置未采集
            $sql = "INSERT INTO " . $tablename . " select * FROM lk_service_oldman";
            DB::unprepared($sql);

            /* $sql=" UPDATE lk_service_oldman odm LEFT JOIN lk_service_orders ors ON ors.oldman_id = odm.id SET odm.collect_time=NULL
            WHERE (odm.live_state='临时外出' AND ors.start_time IS NULL);"; */
            $sql = " UPDATE  `lk_service_oldman` SET collect_time = NULL,live_state='正常' WHERE   live_state !='正常'
				AND live_state !='取消资格' AND live_state !='入住精神病院' AND live_state !='入住敬老院' AND live_state !='死亡' AND (cj IS NULL or cj='不合格')";
            DB::unprepared($sql); //采集不合格的，重新采集

            //$sql="update lk_service_oldman set live_state='正常' where  live_state='临时外出' ";
            $sql = " UPDATE  `lk_service_oldman` SET live_state='正常'  WHERE live_state !='正常' AND live_state !='取消资格'
						AND live_state !='入住精神病院'  AND live_state !='入住敬老院' AND live_state !='自愿放弃服务' AND live_state !='死亡'  AND cj='合格' ";
            DB::unprepared($sql); //采集合格且不为正常状态的，可以服务
        }
    }
    /**
     * 根据住院护理orderId获取服务项目
     */
    public static function selectOrderItemFromBeianOrdersId($id, $man_state)
    {
        //DB::connection()->enableQueryLog();
        $orders_item_manstate = DB::select("SELECT
        si.need_number,si.need_pic_count,si.id,si.str_value,
          si.id AS item_id,
          si.period_nums AS period_nums,
          si.menu_type,si.name,
        COALESCE(COUNT(oi.item_id), 0) AS done_times
      FROM lk_service_oldman om
      JOIN pgm_beian_orders o ON om.id = o.oldman_id
      JOIN pgm_beian_items si
      LEFT JOIN pgm_beian_orders_items oi ON o.id = oi.order_id AND si.id = oi.item_id AND o.id !=?
      WHERE om.id = (SELECT oldman_id FROM pgm_beian_orders WHERE id=?)
      GROUP BY si.id
      ORDER BY si.id;", [$id, $id]);
        //dd(DB::getQueryLog());
        $a_item = Auth::jiangweiArr($orders_item_manstate, "menu_type");
        $res['items'] = $a_item ? $a_item : new def();
        $res['all_items'] = $orders_item_manstate;
        return $res;
    }

    /**
     * 获取当前周期内自理类型的所有服务计划
     */
    public static function getOrderItemsPlanByManState($man_state)
    {
        $orders_item_manstate = DB::select("SELECT * FROM `lk_service_items_plans`
        WHERE man_state=? AND start_day<=DAY(NOW()) AND end_day>=DAY(NOW())
        ORDER BY is_all DESC
        ", [$man_state]);
        return $orders_item_manstate;
    }

    /**
     * 根据指定value获取系统配置信息
     */
    public static function getSysConFromValue($val)
    {
        //dd($id);
        $res = DB::table("lk_system_config")
            ->where("option_key", $val)
            ->first();
        return $res;
    }

    /**
     * 根据orderId获取老人信息/订单信息
     */
    public static function getOldmanInfoFromOrderId($rid)
    {
        //dd($id);
        $res = DB::table("lk_service_orders")
            ->leftJoin("lk_service_oldman", "lk_service_orders.oldman_id", "=", "lk_service_oldman.id")
            ->select('lk_service_oldman.*', 'lk_service_orders.start_time')
            ->where("lk_service_orders.id", "=", $rid)
            ->first();
        return $res;
    }
    /**
     * 根据住院护理beianId获取老人信息/订单信息
     */
    public static function getOldmanInfoFromBeianOrderId($rid)
    {
        //dd($id);
        // DB::connection()->enableQueryLog();
        $res = DB::table("pgm_beian")
            ->leftJoin("lk_service_oldman", "pgm_beian.oldman_id", "=", "lk_service_oldman.id")
            ->select('lk_service_oldman.*')
            ->where("pgm_beian.id", "=", $rid)
            ->first();
        //dd(DB::getQueryLog());
        return $res;
    }
    /**
     * 根据住院护理orderId获取老人信息/订单信息
     */
    public static function getOldmanInfoFromBeianOrderId_Orders($rid)
    {
        //dd($id);
        //DB::connection()->enableQueryLog();
        $res = DB::table("pgm_beian_orders")
            ->leftJoin("lk_service_oldman", "pgm_beian_orders.oldman_id", "=", "lk_service_oldman.id")
            ->select('lk_service_oldman.*', 'pgm_beian_orders.start_time')
            ->where("pgm_beian_orders.id", "=", $rid)
            ->first();
        return $res;
        //dd(DB::getQueryLog());
    }
}
