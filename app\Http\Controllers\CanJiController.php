<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Models\CanJiModel;
use App\Rules\Id;

//残疾人信息填报Controller
class CanJiController extends Controller
{
    //
    public function selectByPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'password' => [
                "required"
            ],
        ], [
            'password.required' => "请输入查询密码！",
        ]);
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        $md5 = $request->input('password');
        $info = CanJiModel::where('md5', $md5)->first();

        if (!$info) {
            $res['msg'] = 1;
            $res['infor'] = '未找到对应的数据！';
            return $res;
        }

        // 根据需要处理查询到的数据
        // $info->CJR_NAME 可以获取残疾人姓名字段的值

        $res['msg'] = 2;
        $res['infor'] = '查询成功！';
        $info->FILE_IDCARD_FRONT = json_decode('{"FILE_IDCARD_FRONT":{"path":"' . $info->FILE_IDCARD_FRONT . '"}}');
        $info->FILE_IDCARD_BACKGROUND = json_decode('{"FILE_IDCARD_BACKGROUND":{"path":"' . $info->FILE_IDCARD_BACKGROUND . '"}}');
        $info->FILE_HUKOUBEN = json_decode('{"FILE_HUKOUBEN":{"path":"' . $info->FILE_HUKOUBEN . '"}}');
        $info->FILE_CJR_HEADER = json_decode('{"FILE_CJR_HEADER":{"path":"' . $info->FILE_CJR_HEADER . '"}}');
        $info->FILE_CJR_MENTOU = json_decode('{"FILE_CJR_MENTOU":{"path":"' . $info->FILE_CJR_MENTOU . '"}}');
        $info->FILE_CJR_SHINEI = json_decode('{"FILE_CJR_SHINEI":{"path":"' . $info->FILE_CJR_SHINEI . '"}}');
        $res['data'] = $info;
        return $res;
    }

    public function saveCanJiInfo(Request $request)
    {
        $regx = '/(^1\d{10}$|^(0\d{2,3}-?|0\d{2,3} )?[1-9]\d{6}?$)/';
        // 中国所有民族列表
        $nationalities = [
            '汉族', '蒙古族', '回族', '藏族', '维吾尔族', '苗族', '彝族', '壮族', '布依族',
            '朝鲜族', '满族', '侗族', '瑶族', '白族', '土家族', '哈尼族', '哈萨克族', '傣族',
            '黎族', '傈僳族', '佤族', '畲族', '高山族', '拉祜族', '水族', '东乡族', '纳西族',
            '景颇族', '柯尔克孜族', '土族', '达斡尔族', '仫佬族', '羌族', '布朗族', '撒拉族',
            '毛南族', '仡佬族', '锡伯族', '阿昌族', '普米族', '塔吉克族', '怒族', '乌孜别克族',
            '俄罗斯族', '鄂温克族', '德昂族', '保安族', '裕固族', '京族', '塔塔尔族', '独龙族',
            '鄂伦春族', '赫哲族', '门巴族', '珞巴族', '基诺族'
        ];
        $level = [
            '一级', '二级', '三级', '四级', '未定级'
        ];
        $CJR_XSYLBXQK = ['享受城镇职工基本医疗保险', '享受城乡居民基本医疗保险', '享受其他保险', '无医疗保险'];
        $validator = Validator::make($request->all(), [
            'UUID' => [
                'required',
                'regex:/^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$/i',
            ],
            'CJR_NAME' => [
                "required", "regex:/\p{Han}/u", "max:5", "min:2"
            ],
            'CJR_IDCARD' => [
                "required", new Id, Rule::unique('canjiren_info', 'CJR_IDCARD')->where(function ($query) use ($request) {
                    return $query->where('CJR_IDCARD', '=', $request->CJR_IDCARD);
                }),
            ],
            'CJR_SEX' => [
                "required", Rule::in(['男', '女'])
            ],
            'CJR_BIRTHDAY' => [
                "required", 'date'
            ],
            'CJR_PHONE' => [
                "required", "regex:{$regx}"
            ],
            'CJR_MINZU' => [
                "required", Rule::in($nationalities)
            ],
            'CJR_NUMBER' => [
                "required", "size:20"
            ],
            'CJR_CATEGORY' => [
                "required"
            ],
            'CJR_LEVEL' => [
                "required", Rule::in($level)
            ],
            'JHR_NAME' => [
                "required", "regex:/\p{Han}/u", "max:5", "min:2"
            ],
            'JHR_IDCARD' => [
                "required", new Id,
            ],
            'JHR_PHONE' => [
                "required", "regex:{$regx}"
            ],
            'CJR_TOWN' => [
                "required", "regex:/\p{Han}/u", "max:10", "min:2"
            ],
            'CJR_VILLAGE' => [
                "required", "regex:/\p{Han}/u", "max:10", "min:2"
            ],
            'CJR_XSYLBXQK' => [
                "required", Rule::in($CJR_XSYLBXQK)
            ],
        ], [
            'UUID.required' => "UUID必传！",
            'UUID.regex' => "UUID格式不正确！",
            'CJR_NAME.required' => "请填写残疾人姓名！",
            'CJR_NAME.regex' => "残疾人姓名必须为中文！",
            'CJR_NAME.max' => "残疾人姓名长度最长为5！",
            'CJR_NAME.min' => "残疾人姓名长度最小为2！",
            'CJR_IDCARD.required' => "请填写残疾人身份证号！",
            'CJR_IDCARD.unique' => "此残疾人身份证号已经存在系统中！不可重复申请！",
            'CJR_SEX.required' => "请选择残疾人性别！",
            'CJR_SEX.in' => "性别只能选择男或者女！",
            'CJR_BIRTHDAY.required' => "请选择残疾人出生日期！",
            'CJR_BIRTHDAY.date' => "出生日期格式不正确！",
            'CJR_PHONE.required' => "请填写残疾人联系电话！",
            'CJR_PHONE.regex' => "残疾人联系电话格式不正确！",
            'CJR_MINZU.required' => "请选择残疾人所属民族！",
            'CJR_MINZU.in' => "残疾人所属民族选择不正确，不在中国所有民族之内！",
            'CJR_NUMBER.required' => "请填写残疾证号码！",
            'CJR_NUMBER.size' => "残疾证号码长度为20位！您填写的格式不对！",
            'CJR_CATEGORY.required' => "请选择残疾类别！可多选！",
            'CJR_LEVEL.required' => "请选择残疾等级！",
            'CJR_LEVEL.in' => "残疾等级选择不正确，不在【一级/二级/三级/四级/未定级】之内！",
            'JHR_NAME.required' => "请填写监护人姓名！",
            'JHR_NAME.regex' => "监护人姓名必须为中文！",
            'JHR_NAME.max' => "监护人姓名长度最长为5！",
            'JHR_NAME.min' => "监护人姓名长度最小为2！",
            'JHR_IDCARD.required' => "请填写监护人身份证号！",
            'JHR_PHONE.required' => "请填写监护人联系电话！",
            'JHR_PHONE.regex' => "监护人联系电话格式不正确！",
            'CJR_TOWN.required' => "请填写残疾人所属街道/镇！",
            'CJR_TOWN.regex' => "残疾人所属街道/镇只能是中文！",
            'CJR_TOWN.max' => "残疾人所属街道/镇的长度不能超过10个字！",
            'CJR_TOWN.min' => "残疾人所属街道/镇的长度不能低于2个字！",
            'CJR_VILLAGE.required' => "请填写残疾人所属社区/村！",
            'CJR_VILLAGE.regex' => "残疾人所属社区/村只能是中文！",
            'CJR_VILLAGE.max' => "残疾人所属社区/村的长度不能超过10个字！",
            'CJR_VILLAGE.min' => "残疾人所属社区/村的长度不能低于2个字！",
            'CJR_XSYLBXQK.required' => "请选择残疾人享受医疗保险情况！",
            'CJR_XSYLBXQK.in' => "残疾人享受医疗保险情况选择不正确，不在【'享受城镇职工基本医疗保险','享受城乡居民基本医疗保险','享受其他保险','无医疗保险'】之内！",
        ]);
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }
        $canjiModel = new CanJiModel();
        $canji = $canjiModel->saveData($request);
        if ($canji) {
            $res['infor'] = "残疾人申请提交成功！";
            $res['msg'] = 2;
            $res['password'] = md5($request->UUID);
            return $res;
        } else {
            $res['infor'] = "残疾人申请提交失败！";
            $res['msg'] = 1;
            return $res;
        }
    }

    //接收图片上传接口
    public function recieveImage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'tp' => [
                "required", Rule::in(['tb'])
            ],
            'father_id' => [
                "required"
            ],
            'f_type' =>  [
                "required", Rule::in([
                    'FILE_IDCARD_FRONT', 'FILE_IDCARD_BACKGROUND', 'FILE_HUKOUBEN', 'FILE_CJR_HEADER', 'FILE_CJR_MENTOU', 'FILE_CJR_SHINEI', 'FILE_CJR_XIANGGUAN1', 'FILE_CJR_XIANGGUAN2', 'FILE_CJR_XIANGGUAN3'
                ])
            ],
        ], [
            'f_type.required' => "参数缺失ft",
            'tp.required' => "缺少参数t",
            'tp.in' => "参数错误t",
            'f_type.in' => "参数错误ft",
            'father_id.required' => "参数缺失f",
        ]);
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        $image = $request->file($request->name);
        //dd($image->getMimeType());
        //dd($image->isValid());
        if (!$request->file() || !$image->isValid() || !str_contains($image->getMimeType(), "image/")) {
            $res['msg'] = 1;
            $res['infor'] = "未收到文件！";
            return $res;
        }

        $filename = $request->father_id . '[zhan]' . $request->f_type . "." . $image->extension();
        $uploadpath = 'uploadcanjiren/' . date('Ymd');
        $tmp = $image->storeAs($uploadpath, $filename, 'public');

        $res['infor'] = "上传成功！";
        $res['path'] = $tmp;
        $res['msg'] = 2;
        return $res;
    }
}
