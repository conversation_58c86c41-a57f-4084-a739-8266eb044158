<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LkServiceOrders extends Model
{
    use HasFactory;
    protected $table = 'lk_service_orders';

    protected $fillable = [
        'dept_id',
        'district_id',
        'oldman_id',
        'man_state',
        'start_time',
        'start_xy',
        'end_time',
        'end_xy',
        'extra_service',
        'qd_uid',
        'add_uid',
        'all_uids',
        'all_uids_num',
        'evaluate_userid',
        'evaluate_time',
        'evaluate_worker',
        'evaluate_phone',
        'evaluate_name',
        'evaluate_words',
        'evaluate_score',
        'addtime',
        'deleted',
        'fwzp',
        'bz',
        'kjid',
        'hf',
        'test',
        'sp',
        'sfly',
        'fwxz',
        'sh',
        'shtime',
        'shtxt',
        'name',
        'idcard',
        'address',
        'spal',
        'zpal',
        'remark',
        'del_flag',
        'create_by',
        'create_time',
        'update_by',
        'update_time',
        'evaluate_eligible',
        'contain_refuse',
        'mzjccrq',
        'invalid_item_ids',
        'state',
        'item_plane',
        'sqid'
    ];

    protected $casts = [
        'start_time' => 'integer',
        'end_time' => 'integer',
        'addtime' => 'integer',
        'evaluate_time' => 'integer',
        'shtime' => 'integer',
        'spal' => 'boolean',
        'zpal' => 'boolean',
        'deleted' => 'boolean',
        'contain_refuse' => 'boolean',
        'create_time' => 'datetime',
        'update_time' => 'datetime',
        'mzjccrq' => 'datetime',
    ];

    protected $dates = [
        'create_time',
        'update_time',
        'mzjccrq',
    ];
    // 定义反向关系
    public function shenqing()
    {
        return $this->belongsTo(LkServiceShenqing::class, 'sqid');
    }
    public function items()
    {
        return $this->belongsTo(LkItemModel::class, 'item_plane');
    }

    protected $primaryKey = 'id';

    public $timestamps = false;
}
