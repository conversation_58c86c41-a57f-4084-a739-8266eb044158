import{r as ref,o as openBlock,c as createElementBlock,a as createBaseVNode,K as Fragment,a9 as renderList,u as unref,L as normalizeClass,S as toDisplayString,M as createBlock,Q as createVNode,O as withCtx,H as withDirectives,U as Transition,B as renderSlot,J as createCommentVNode,I as vShow,$ as Teleport,aj as createTextVNode,W as onUpdated,ab as vModelText,ak as Suspense,al as withAsyncContext,s as isRef,am as vModelCheckbox,A as defineComponent,f as computed,e as onMounted,R as withModifiers,ae as createApp}from"./@vue-d2c97bde.js";import{u as useRouter,a as useRoute,R as RouterView,c as createRouter,b as createWebHashHistory}from"./vue-router-fd47a73e.js";import"./uid-42d16d8d.js";import{a as axios}from"./axios-c47e0cf1.js";import{E as ElMessage,a as ElLoading,b as ElMessageBox,c as ElProgress,v as vLoading,d as ElUpload,e as ElOption,f as ElSelect}from"./element-plus-e812ece0.js";import{i as imageConversionExports}from"./image-conversion-cfc1aedf.js";import"./form-data-d2a9677b.js";import"./@vueuse-83e2110d.js";import"./@popperjs-c75af06c.js";import"./lodash-es-f949ca56.js";import"./@element-plus-9a95e4fa.js";import"./@ctrl-43a4208a.js";import"./dayjs-28e086c5.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const a of o)if(a.type==="childList")for(const n of a.addedNodes)n.tagName==="LINK"&&n.rel==="modulepreload"&&s(n)}).observe(document,{childList:!0,subtree:!0});function l(o){const a={};return o.integrity&&(a.integrity=o.integrity),o.referrerpolicy&&(a.referrerPolicy=o.referrerpolicy),o.crossorigin==="use-credentials"?a.credentials="include":o.crossorigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function s(o){if(o.ep)return;o.ep=!0;const a=l(o);fetch(o.href,a)}})();const all_min="",_hoisted_1$m={class:"sticky bottom-0 bg-white w-full"},_hoisted_2$g={class:"grid grid-flow-col grid-cols-4 gap-1 text-center text-white text-lg"},_hoisted_3$d=["onClick"],_sfc_main$q={__name:"BottomNavigation",props:{state:{type:String,default:"cj"}},setup(t){const e=t,l=useRouter(),s=ref([]);return(()=>{s.value=[],s.value.push({name:"采集",icon:"fa-solid fa-camera",routeName:"home",activated:"cj"}),s.value.push({name:"服务",icon:"fa-solid fa-car-side",routeName:"services",activated:"fw"}),s.value.push({name:"统计",icon:"fa-solid fa-chart-pie",routeName:"tongJi",activated:"tj"}),s.value.push({name:"本组",icon:"fa-solid fa-users",routeName:"benZu",activated:"bz"})})(),(a,n)=>(openBlock(),createElementBlock("footer",_hoisted_1$m,[createBaseVNode("div",_hoisted_2$g,[(openBlock(!0),createElementBlock(Fragment,null,renderList(s.value,c=>(openBlock(),createElementBlock("div",{onClick:d=>unref(l).push({name:c.routeName}),class:normalizeClass(`p-1 rounded-2xl border-2 border-red-50 border-opacity-50 items-center
              duration-150 cursor-pointer grid grid-flow-col grid-cols-2
            ${e.state==c.activated?" bg-cj_bg":" bg-bottom_bg"}`),key:c.routeName},[createBaseVNode("i",{class:normalizeClass(c.icon)},null,2),createBaseVNode("label",null,toDisplayString(c.name),1)],10,_hoisted_3$d))),128))])]))}},BaseModal_vue_vue_type_style_index_0_scoped_bdbc115a_lang="",_export_sfc=(t,e)=>{const l=t.__vccOpts||t;for(const[s,o]of e)l[s]=o;return l},_hoisted_1$l={class:"fixed w-full bg-black bg-opacity-30 h-full top-0 left-0 flex justify-center px-8 overflow-y-auto py-6"},_hoisted_2$f={key:0,class:"p-4 bg-white self-start mt-32 max-w-screen-md"},_sfc_main$p={__name:"BaseModal",props:{showModal:{type:Boolean,default:!1}},emits:["close-modal"],setup(t){return(e,l)=>(openBlock(),createBlock(Teleport,{to:"body"},[createVNode(Transition,{name:"modal-outer"},{default:withCtx(()=>[withDirectives(createBaseVNode("div",_hoisted_1$l,[createVNode(Transition,{name:"modal-inner"},{default:withCtx(()=>[t.showModal?(openBlock(),createElementBlock("div",_hoisted_2$f,[renderSlot(e.$slots,"default",{},void 0,!0),createBaseVNode("button",{onClick:l[0]||(l[0]=s=>e.$emit("close-modal")),class:"text-white mt-8 bg-weather-secondary py-2 px-6 w-full"},"关闭")])):createCommentVNode("",!0)]),_:3})],512),[[vShow,t.showModal]])]),_:3})]))}},BaseModal=_export_sfc(_sfc_main$p,[["__scopeId","data-v-bdbc115a"]]);function request(t){const e=axios.create({baseURL:"/api",timeout:5e3});return e.interceptors.request.use(l=>l,l=>{}),e.interceptors.response.use(l=>(l.data.errorCode&&(t.router.push({name:"login"}),ElMessage.error("登录信息已失效，请重新登录")),l.data),l=>{ElMessage.error("请求失败")}),e(t)}const doLogin=(t,e)=>request({url:"/login/doLogin",method:"post",data:{username:t,password:e}}),logOut=()=>request({url:"/login/logOut",method:"post"}),getOldmanList_collection_byName=(t,e)=>request({url:"/getinfo/getOldmanList_collection",method:"post",router:e,data:{oldman_name:t}}),getTown=t=>request({url:"/getinfo/townList",method:"post",router:t}),getVillage=(t,e)=>request({url:"/getinfo/villageList",method:"post",router:e,data:{town:t}}),getOldmanList_collection=(t,e,l)=>request({url:"/getinfo/getOldmanList_collection",method:"post",router:l,data:{town:t,village:e}}),getOldmanList_services=(t,e,l,s,o)=>request({url:"/getinfo/getOldmanList_services",method:"post",router:o,data:{town:t,village:e,state:l,name:s}}),order_start=(t,e)=>request({url:"/saveinfo/orderStart_services",method:"post",router:e,data:t}),order_end=(t,e)=>request({url:"/saveinfo/orderEnd_services",method:"post",router:e,data:t}),getOldmanInfo_collection=(t,e)=>request({url:"/getinfo/getOldmanInfo_collection",method:"post",router:e,data:{id:t}}),getOrderInfo_services=(t,e)=>request({url:"/getinfo/getOrderInfo_services",method:"post",router:e,data:{id:t}}),saveOldmanInfo_collection=(t,e)=>request({url:"/saveinfo/saveOldmanInfo_collection",method:"post",router:e,data:t}),getSysConfig=(t,e)=>request({url:"/getinfo/getSysConfig",method:"post",router:e,data:t}),delImage=(t,e)=>request({url:"/upload/delimg",method:"post",router:e,data:t}),defaultOptions={lock:!0,text:"正在加载",background:"rgba(0, 0, 0, 0.1)"},withLoading=(t,e={})=>{let l;const s=c=>{l=ElLoading.service(c)},o=()=>{l&&l.close()},a=Object.assign(defaultOptions,e);return(...c)=>{try{s(a);const d=t(...c);return d instanceof Promise?d.then(r=>(o(),r)).catch(r=>{throw o(),r}):(o(),d)}catch(d){throw o(),d}}},_hoisted_1$k={class:"sticky top-0 bg-weather-primary shadow-lg z-10"},_hoisted_2$e={class:"container px-2 flex flex-row items-center text-black py-2"},_hoisted_3$c={class:"flex items-center gp-3 flex-1"},_hoisted_4$a=createBaseVNode("div",{class:"flex gp-3 flex-1 justify-center"},[createBaseVNode("i",{class:"fa-solid fa-arrow-rotate-right",onclick:"location.reload()"})],-1),_hoisted_5$7=createBaseVNode("div",{class:"text-black"},[createBaseVNode("h1",{class:"text-2xl mb-1"},"帮助:"),createBaseVNode("p",{class:"mb-4"}," 本系统已经重新构建，提升了性能，希望这能提高您的效率，增进使用体验。 "),createBaseVNode("h2",{class:"text-2xl"},"服务流程:"),createBaseVNode("ol",{class:"list-decimal list-inside mb-4"},[createBaseVNode("li",null," 【采集功能】： 首先您要先去采集待服务人员信息，点击最下方的【采集】按钮。 然后选择您要去服务的乡镇-行政村，从待服务人员列表中找到您要采集的待服务人员。 点击待服务人员，进入采集界面。 填写所有必填的内容。 然后提交信息，采集结束。 "),createBaseVNode("li",null," 【服务功能】： 服务的前提是采集信息的流程您已经走完了。 首先点击最下方的【服务】按钮，然后选择您要去服务的乡镇-行政村，从待服务人员列表中找到您要服务的待服务人员。 点击待服务人员，进入服务界面，点击上门签到。 将服务过程中的服务照片和服务视频上传到系统内，填写所有需要填的备注内容。 然后点击服务签退，服务流程结束。 "),createBaseVNode("li",null," 【统计功能】：********** "),createBaseVNode("li",null," 【本组成员功能】：********** ")]),createBaseVNode("h2",{class:"text-2xl"},"出现问题怎么办"),createBaseVNode("p",null,[createTextVNode(" 首先要保障您所在的位置信号很好。 建议先进行刷新，或者重新登陆系统。 如果仍未解决问题，请联系"),createBaseVNode("a",{class:"text-blue-500",href:"tel:13255402508"},"秦经理")])],-1),_sfc_main$o={__name:"SiteNavigation",setup(t){ref([]),useRoute();const e=useRouter(),l=async()=>{await ElMessageBox.confirm("您确认要退出登录吗?","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{(await withLoading(logOut)()).msg=="2"&&e.push({name:"login"})}).catch(()=>{})},s=ref(null),o=()=>{s.value=!s.value};return(a,n)=>(openBlock(),createElementBlock("header",_hoisted_1$k,[createBaseVNode("nav",_hoisted_2$e,[createBaseVNode("div",_hoisted_3$c,[createBaseVNode("i",{class:"fa-solid fa-angle-left text-2xl",onClick:n[0]||(n[0]=c=>unref(e).go(-1))})]),_hoisted_4$a,createBaseVNode("div",{class:"flex gp-3 flex-1 justify-end"},[createBaseVNode("i",{onClick:o,class:"fa-solid fa-question text-xl hover:bg-weather-secondary duration-150 cursor-pointer"})]),createBaseVNode("div",{class:"flex gp-3 flex-1 justify-end"},[createBaseVNode("i",{onClick:l,class:"fa-solid fa-arrow-right-to-bracket"})]),createVNode(BaseModal,{showModal:s.value,onCloseModal:o},{default:withCtx(()=>[_hoisted_5$7]),_:1},8,["showModal"])])]))}},_hoisted_1$j={class:"flex flex-col min-h-screen font-Robot bg-weather-primary"},_sfc_main$n={__name:"App",setup(t){const e=ref("cj"),l=useRoute();return onUpdated(()=>{switch(l.name){case"collectionView":e.value=l.params.type;break;case"services":e.value="fw";break;default:e.value="cj";break}}),(s,o)=>(openBlock(),createElementBlock("div",_hoisted_1$j,[unref(l).name!=="login"&&unref(l).name!=="logins"?(openBlock(),createBlock(_sfc_main$o,{key:0})):createCommentVNode("",!0),createVNode(unref(RouterView),{class:"flex-1"}),unref(l).name!=="login"&&unref(l).name!=="logins"?(openBlock(),createBlock(_sfc_main$q,{key:1,state:e.value},null,8,["state"])):createCommentVNode("",!0)]))}},_hoisted_1$i={class:"w-full text-center bg-cj_bg rounded-b-lg text-white sticky top-12 z-50"},_sfc_main$m={__name:"UrlState",props:{name:{type:String,default:"采集"},lujing:{type:String,default:"待服务人员列表"}},setup(t){return(e,l)=>(openBlock(),createElementBlock("div",_hoisted_1$i,toDisplayString(t.name)+"->"+toDisplayString(t.lujing),1))}},_sfc_main$l={},_hoisted_1$h={class:"animate-pulse bg-gradient-to-r from-gray-100"};function _sfc_render(t,e){return openBlock(),createElementBlock("div",_hoisted_1$h,"   ")}const AnimatedPlaceholder=_export_sfc(_sfc_main$l,[["render",_sfc_render]]),_hoisted_1$g={class:"flex py-6 px-3 bg-primary-green rounded-md shadow-md"},_hoisted_2$d={class:"flex flex-col flex-1 gap-2"},_hoisted_3$b={class:"flex flex-col items-end flex-1 gap-2"},_sfc_main$k={__name:"CityCardSkeleton",setup(t){return(e,l)=>(openBlock(),createElementBlock("div",_hoisted_1$g,[createBaseVNode("div",_hoisted_2$d,[createVNode(AnimatedPlaceholder,{class:"max-w-[50%]"}),createVNode(AnimatedPlaceholder,{class:"max-w-[40%]"})]),createBaseVNode("div",_hoisted_3$b,[createVNode(AnimatedPlaceholder,{class:"max-w-[50px] w-full"}),createVNode(AnimatedPlaceholder,{class:"max-w-[75px] w-full"})])]))}},_hoisted_1$f={class:"flex flex-col flex-1"},_hoisted_2$c={class:"text-3xl"},_hoisted_3$a=["innerHTML"],_hoisted_4$9={class:"text-xl"},_hoisted_5$6={class:"flex flex-col gap-2"},_hoisted_6$5={class:"text-3xl self-end flex flex-1 items-center flex-col"},_hoisted_7$4=["src"],_hoisted_8$4={class:"text-base"},_sfc_main$j={__name:"CityCard",props:{oldman:{type:Object,default:null}},setup(t){const e=' this.src=" unknown.jpeg " ';return(l,s)=>(openBlock(),createElementBlock("div",{class:normalizeClass("text-white flex py-6 px-3 "+(t.oldman.live_state==="正常"?"bg-gradient-to-r from-green-700 to-blue-500 ":"bg-gradient-to-r from-red-700 to-orange-500 ")+"rounded-md shadow-md cursor-pointer")},[createBaseVNode("div",_hoisted_1$f,[createBaseVNode("h2",_hoisted_2$c,toDisplayString(t.oldman.name),1),createBaseVNode("h3",{innerHTML:t.oldman.hasOwnProperty("end_time")?"开始服务时间："+t.oldman.start_time+"<br/>服务时长："+t.oldman.totalTime+"分钟":"生活状态："+t.oldman.live_state},null,8,_hoisted_3$a),createBaseVNode("h3",_hoisted_4$9,toDisplayString(t.oldman.address),1)]),createBaseVNode("div",_hoisted_5$6,[createBaseVNode("p",_hoisted_6$5,[createTextVNode(toDisplayString(t.oldman.collect_type)+" ",1),createBaseVNode("img",{class:"w-[80px] h-[80px] object-cover rounded-2xl",onerror:e,src:`${t.oldman.zp===null?"unknown.jpeg":t.oldman.zp}`,alt:""},null,8,_hoisted_7$4),createBaseVNode("label",_hoisted_8$4,toDisplayString(t.oldman.collect_time===null?"未采集":t.oldman.cj),1)])])],2))}},_hoisted_1$e={key:0},_sfc_main$i={__name:"CityList",props:{oldmanList:{type:Array,default:ref([])}},emits:["click_check"],setup(t){return(e,l)=>(openBlock(),createElementBlock(Fragment,null,[(openBlock(!0),createElementBlock(Fragment,null,renderList(t.oldmanList,(s,o)=>(openBlock(),createElementBlock("div",{key:o},[createVNode(_sfc_main$j,{oldman:s,onClick:a=>{e.$emit("click_check",s)}},null,8,["oldman","onClick"])]))),128)),t.oldmanList.length===0?(openBlock(),createElementBlock("p",_hoisted_1$e," 未搜索到任何待服务人员。 ")):createCommentVNode("",!0)],64))}},_hoisted_1$d={class:"pt-4 mb-8 relative grid grid-flow-col grid-cols-none items-center"},_hoisted_2$b=["value"],_hoisted_3$9={key:0,class:"absolute text-white bg-cj_bg w-full shadow-md py-2 px-1 top-[66px] rounded-md max-h-96 overflow-y-auto"},_hoisted_4$8=["onClick"],_sfc_main$h={__name:"AddressSelect",props:{defaultValue:{type:String,default:"请选择"},showList:{type:Boolean,default:!1},messageResults:{type:Array,default:[]},lable_v:{type:String,default:""}},emits:["selectChange"],setup(t){const e=t;return(l,s)=>(openBlock(),createElementBlock("div",_hoisted_1$d,[createBaseVNode("span",null,toDisplayString(e.lable_v),1),createBaseVNode("input",{value:e.defaultValue,type:"button",onClick:s[0]||(s[0]=o=>t.showList=!t.showList),class:"py-2 px-1 w-full bg-transparent border-b focus:border-weather-secondary focus:outline-none focus:shadow-[0px_1px_0_0_#004E71]"},null,8,_hoisted_2$b),t.messageResults&&t.showList?(openBlock(),createElementBlock("ul",_hoisted_3$9,[t.messageResults.length===0?(openBlock(),createElementBlock("p",{key:0,class:"py-2 cursor-not-allowed text-center",onClick:s[1]||(s[1]=o=>t.showList=!t.showList)},"抱歉，没有任何结果")):(openBlock(!0),createElementBlock(Fragment,{key:1},renderList(t.messageResults,(o,a)=>(openBlock(),createElementBlock("li",{key:o.redisKey,class:"py-2 cursor-pointer shadow-md text-center",onClick:n=>{l.$emit("selectChange",o),t.showList=!t.showList}},toDisplayString(o.name),9,_hoisted_4$8))),128))])):createCommentVNode("",!0)]))}},_hoisted_1$c={class:"grid grid-cols-2 items-center"},_sfc_main$g={__name:"AddressComponent",props:{val:{type:Object,default:{townSelect:"请选择乡镇",villageSelect:"请选择行政村"}}},emits:["returnAddress"],setup(t,{expose:e,emit:l}){const s=useRouter(),o=ref([]),a=ref("请选择乡镇"),n=ref("请选择行政村");e({townSelect:a,villageSelect:n});const c=ref([]),d=async f=>{f.type==="town"?(a.value=f.name,n.value="请选择行政村",m()):f.type==="village"&&(n.value=f.name,l("returnAddress",a.value,n.value))},h=()=>new Promise((f,_)=>{getTown(s).then(i=>{try{if(i.msg==1||i.hasOwnProperty("errorCode")){_(ElMessage.error(i.infor));return}o.value=i.list,o.value.length>0&&d({type:"town",name:o.value[0].name})}catch(p){ElMessage.error(p.message)}})}),r=()=>new Promise((f,_)=>{f(getVillage(a.value,s).then(i=>{if(i.msg==1||i.hasOwnProperty("errorCode")){_(ElMessage.error(i.msg));return}c.value=i.list,c.value.length>0&&d({type:"village",name:c.value[0].name})}))}),u=async()=>{try{await withLoading(h)()}catch(f){console.log(f)}},m=async()=>{try{await withLoading(r)()}catch(f){console.log(f)}};return u(),(f,_)=>(openBlock(),createElementBlock("div",_hoisted_1$c,[createVNode(_sfc_main$h,{lable_v:"乡：",class:"px-2",defaultValue:a.value,messageResults:o.value,showList:!1,onSelectChange:d},null,8,["defaultValue","messageResults"]),createVNode(_sfc_main$h,{lable_v:"村：",class:"px-2",defaultValue:n.value,messageResults:c.value,showList:!1,onSelectChange:d},null,8,["defaultValue","messageResults"])]))}},_hoisted_1$b={key:0,style:{background:"#2854a7"},class:"text-white w-full shadow-md py-1 px-1 rounded-md"},_hoisted_2$a={key:0,class:"py-2"},_hoisted_3$8={key:1,class:"py-2"},_hoisted_4$7=["onClick"],_sfc_main$f={__name:"SelectOldman",props:{searchQuery:{type:Object,default:{value:""}},searchError:{type:Boolean,default:!1},messageResults:{type:Object,default:ref(null)}},emits:["on_input","preview"],setup(t){const e=t;return(l,s)=>(openBlock(),createElementBlock(Fragment,null,[withDirectives(createBaseVNode("input",{"onUpdate:modelValue":s[0]||(s[0]=o=>e.searchQuery.value=o),type:"text",onInput:s[1]||(s[1]=o=>l.$emit("on_input")),placeholder:"请输入待服务人员名称进行搜索",class:"py-2 px-1 w-full bg-transparent border-b focus:border-weather-secondary focus:outline-none focus:shadow-[0px_1px_0_0_#004E71]"},null,544),[[vModelText,e.searchQuery.value]]),e.messageResults&&e.messageResults!=""?(openBlock(),createElementBlock("ul",_hoisted_1$b,[t.searchError?(openBlock(),createElementBlock("p",_hoisted_2$a,"抱歉，搜索出错了，请您再试一次")):createCommentVNode("",!0),!t.searchError&&t.messageResults.length===0?(openBlock(),createElementBlock("p",_hoisted_3$8,"抱歉，没有搜索到任何结果")):(openBlock(!0),createElementBlock(Fragment,{key:2},renderList(t.messageResults,(o,a)=>(openBlock(),createElementBlock("li",{key:o.id,class:normalizeClass("rounded-md py-2 my-1 cursor-pointer shadow-2xl "+(o.live_state==="正常"?" bg-green_bg ":"bg-red_bg  ")),onClick:n=>l.$emit("preview",o)},toDisplayString(o.name)+"["+toDisplayString(o.live_state)+"]--"+toDisplayString(o.address),11,_hoisted_4$7))),128))])):createCommentVNode("",!0)],64))}},_hoisted_1$a={class:"px-2 w-full text-black pb-12 pt-2"},_hoisted_2$9={class:"pb-12 mb-2 relative"},_hoisted_3$7={class:"flex flex-col gap-4"},_sfc_main$e={__name:"HomeView",setup(t){const e=ref([]),l=useRouter(),s=(u,m,f)=>new Promise((_,i)=>{_(getOldmanList_collection(u,m,f).then(p=>{if(p.msg==1){i(ElMessage.error(p.msg));return}e.value=p.list}))}),o=async(u,m)=>{try{await withLoading(s)(u,m,l)}catch(f){ElMessage.error(f.message)}},a=ref({}),n=ref(null),c=ref(null),d=ref(null),h=u=>{l.push({name:"collectionView",params:{id:u.id,type:"cj"}})},r=()=>{clearTimeout(n.value),n.value=setTimeout(async()=>{if(a.value.value!==""){try{const u=await getOldmanList_collection_byName(a.value.value,l).then(m=>m);if(u.msg==1){ElMessage.error(u.infor),c.value=null;return}c.value=u.list}catch(u){ElMessage.error(u.message),d.value=!0}return}c.value=null},500)};return(u,m)=>(openBlock(),createElementBlock("main",_hoisted_1$a,[createVNode(_sfc_main$m),createBaseVNode("div",_hoisted_2$9,[(openBlock(),createBlock(Suspense,null,{fallback:withCtx(()=>[createVNode(_sfc_main$k)]),default:withCtx(()=>[createVNode(_sfc_main$g,{onReturnAddress:o})]),_:1})),createVNode(_sfc_main$f,{onPreview:h,onOn_input:r,"search-query":a.value,"message-results":c.value},null,8,["search-query","message-results"])]),withDirectives(createBaseVNode("div",_hoisted_3$7,[(openBlock(),createBlock(Suspense,null,{fallback:withCtx(()=>[createVNode(_sfc_main$k)]),default:withCtx(()=>[createVNode(_sfc_main$i,{oldmanList:e.value,onClick_check:h},null,8,["oldmanList"])]),_:1}))],512),[[vShow,!c.value]])]))}},_hoisted_1$9={class:"flex flex-col flex-1 items-center"},_hoisted_2$8={key:0,class:"text-white p-4 bg-weather-secondary w-full text-center"},_hoisted_3$6=createBaseVNode("p",null," 您当前正在预览天气，点击+号将此城市加入您的收藏！ ",-1),_hoisted_4$6=[_hoisted_3$6],_hoisted_5$5={class:"flex flex-col items-center text-white py-12"},_hoisted_6$4={class:"text-4xl mb-2"},_hoisted_7$3={class:"text-sm mb-12"},_hoisted_8$3={class:"text-8xl mb-8"},_hoisted_9$3={class:"capitalize"},_hoisted_10$3=["src"],_hoisted_11$3=createBaseVNode("hr",{class:"border-white border-opacity-10 border w-full"},null,-1),_hoisted_12$3={class:"max-w-screen-md w-full py-12"},_hoisted_13$3={class:"mx-8 text-white"},_hoisted_14$2=createBaseVNode("h2",{class:"mb-4"},"小时级别天气",-1),_hoisted_15$2={class:"flex gap-20 overflow-x-scroll"},_hoisted_16$2={class:"whitespace-nowrap text-md"},_hoisted_17$2={class:"text-md text-white"},_hoisted_18$2=["src"],_hoisted_19$2={class:"text-xl"},_hoisted_20$2=createBaseVNode("hr",{class:"border-white border-opacity-10 border w-full"},null,-1),_hoisted_21$2={class:"max-w-screen-md w-full py-12"},_hoisted_22$2={class:"mx-8 text-white"},_hoisted_23$2=createBaseVNode("h2",{class:"mb-4"},"7天 天气",-1),_hoisted_24$2={class:"flex-1"},_hoisted_25$2=["src"],_hoisted_26$2={class:"flex gap-2 flex-1 justify-end"},_hoisted_27$2=createBaseVNode("i",{class:"fa-solid fa-trash"},null,-1),_hoisted_28$2=createBaseVNode("p",null,"移除城市",-1),_hoisted_29$2=[_hoisted_27$2,_hoisted_28$2],_sfc_main$d={__name:"AsyncCityView",async setup(t){let e,l;const s=useRoute(),o=async()=>{try{const d=await axios.get(`https://api.openweathermap.org/data/3.0/onecall?lat=${s.query.lat}&lon=${s.query.lng}&exclude={part}&appid=1eeeac6388f7e48ea1111bcc7dbce220&units=metric&lang=zh_cn`),h=new Date().getTimezoneOffset()*6e4,r=d.data.current.dt*1e3+h;return d.data.currentTime=r+1e3*d.data.timezone_offset,d.data.hourly.forEach(u=>{const m=u.dt*1e3+h;u.currentTime=m+1e3*d.data.timezone_offset}),d.data}catch(d){console.log(d)}},a=([e,l]=withAsyncContext(()=>o()),e=await e,l(),e),n=useRouter(),c=()=>{const h=JSON.parse(localStorage.getItem("savedCities")).filter(r=>r.id!==s.query.id);localStorage.setItem("savedCities",JSON.stringify(h)),n.push({name:"home"})};return(d,h)=>(openBlock(),createElementBlock("div",_hoisted_1$9,[unref(s).query.preview?(openBlock(),createElementBlock("div",_hoisted_2$8,_hoisted_4$6)):createCommentVNode("",!0),createBaseVNode("div",_hoisted_5$5,[createBaseVNode("h1",_hoisted_6$4,toDisplayString(unref(s).params.city),1),createBaseVNode("p",_hoisted_7$3,toDisplayString(new Date(unref(a).currentTime).toLocaleDateString("zh-CN",{weekday:"short",day:"2-digit",month:"long"}))+" "+toDisplayString(new Date(unref(a).currentTime).toLocaleTimeString("zh-CN",{timeStyle:"short"})),1),createBaseVNode("p",_hoisted_8$3,toDisplayString(Math.round(unref(a).current.temp))+"° ",1),createBaseVNode("p",null," 体感温度 "+toDisplayString(Math.round(unref(a).current.feels_like))+" ° ",1),createBaseVNode("p",_hoisted_9$3,toDisplayString(unref(a).current.weather[0].description),1),createBaseVNode("img",{class:"w-[150px] h-auto",src:`http://openweathermap.org/img/wn/${unref(a).current.weather[0].icon}@2x.png`,alt:""},null,8,_hoisted_10$3)]),_hoisted_11$3,createBaseVNode("div",_hoisted_12$3,[createBaseVNode("div",_hoisted_13$3,[_hoisted_14$2,createBaseVNode("div",_hoisted_15$2,[(openBlock(!0),createElementBlock(Fragment,null,renderList(unref(a).hourly,r=>(openBlock(),createElementBlock("div",{key:r.dt,class:"flex flex-col gap-4 items-center"},[createBaseVNode("p",_hoisted_16$2,toDisplayString(new Date(r.dt*1e3).toLocaleTimeString("zh-CN",{day:"2-digit",month:"long",hour:"numeric"})),1),createBaseVNode("p",_hoisted_17$2,toDisplayString(r.weather[0].description),1),createBaseVNode("img",{class:"w-auto h-[50px] object-cover",src:`http://openweathermap.org/img/wn/${r.weather[0].icon}@2x.png`,alt:""},null,8,_hoisted_18$2),createBaseVNode("p",_hoisted_19$2,toDisplayString(Math.round(r.temp))+"° ",1)]))),128))])])]),_hoisted_20$2,createBaseVNode("div",_hoisted_21$2,[createBaseVNode("div",_hoisted_22$2,[_hoisted_23$2,(openBlock(!0),createElementBlock(Fragment,null,renderList(unref(a).daily,r=>(openBlock(),createElementBlock("div",{key:r.dt,class:"flex items-center"},[createBaseVNode("p",_hoisted_24$2,toDisplayString(new Date(r.dt*1e3).toLocaleDateString("zh-CN",{day:"numeric",month:"long"}))+" "+toDisplayString(new Date(r.dt*1e3).toLocaleDateString("zh-CN",{weekday:"long"})),1),createBaseVNode("img",{class:"w-[50px] h-[50px] object-cover",src:`http://openweathermap.org/img/wn/${r.weather[0].icon}@2x.png`,alt:""},null,8,_hoisted_25$2),createTextVNode(" "+toDisplayString(r.weather[0].description)+" ",1),createBaseVNode("div",_hoisted_26$2,[createBaseVNode("p",null,toDisplayString(Math.round(r.temp.max))+"℃ ~ "+toDisplayString(Math.round(r.temp.min))+"℃",1)])]))),128))])]),unref(s).query.preview?createCommentVNode("",!0):(openBlock(),createElementBlock("div",{key:1,class:"flex items-center gap-2 py-12 text-white cursor-pointer duration-150 hover:text-red-500",onClick:c},_hoisted_29$2))]))}},_hoisted_1$8={class:"flex flex-col flex-1"},_hoisted_2$7={class:"flex flex-col py-12 items-center"},_hoisted_3$5={class:"flex flex-col py-12 px-8 items-center"},_hoisted_4$5={class:"flex flex-col py-12 px-8 items-center"},_sfc_main$c={__name:"CityViewSkeleton",setup(t){return(e,l)=>(openBlock(),createElementBlock("div",_hoisted_1$8,[createBaseVNode("div",_hoisted_2$7,[createVNode(AnimatedPlaceholder,{class:"max-w-[300px] w-full mb-2"}),createVNode(AnimatedPlaceholder,{class:"max-w-[300px] w-full mb-12"}),createVNode(AnimatedPlaceholder,{class:"max-w-[300px] h-[100px] w-full mb-12"}),createVNode(AnimatedPlaceholder,{class:"max-w-[300px] w-full mb-8"}),createVNode(AnimatedPlaceholder,{class:"max-w-[300px] h-[75px] w-full"})]),createBaseVNode("div",_hoisted_3$5,[createVNode(AnimatedPlaceholder,{class:"max-w-screen-md h-[100px] w-full mb-12"})]),createBaseVNode("div",_hoisted_4$5,[createVNode(AnimatedPlaceholder,{class:"max-w-screen-md h-[100px] w-full mb-12"})])]))}},_sfc_main$b={__name:"CityView",setup(t){return(e,l)=>(openBlock(),createElementBlock("div",null,[(openBlock(),createBlock(Suspense,null,{default:withCtx(()=>[createVNode(_sfc_main$d)]),fallback:withCtx(()=>[createVNode(_sfc_main$c)]),_:1}))]))}},_hoisted_1$7={class:"bg-no-repeat bg-center bg-cover absolute w-full bg-black bg-opacity-30 h-screen top-0 left-0 flex justify-center overflow-y-auto",style:{"background-image":"url('./bg.png')"}},_hoisted_2$6={style:{"background-image":"url('./fg.png')","background-position-y":"0%"},class:"py-12 bg-no-repeat bg-center bg-contain w-full sm:w-2/3 md:w-3/5 bg-opacity-30 h-full px-8"},_hoisted_3$4={class:"grid grid-flow-row grid-rows-4 max-h-64 my-32 w-full h-full items-center border-gray-50 text-xl"},_hoisted_4$4={class:"grid grid-flow-col grid-cols-2 px-8 py-2 items-center"},_hoisted_5$4=createBaseVNode("label",{for:"rem",class:"flex-1"},"记住密码",-1),_hoisted_6$3=["disabled"],_sfc_main$a={__name:"LoginContainer",async setup(t){let e,l;const[s,o]=[ref(!1),ref(!1)],[a,n]=[ref(""),ref("")],c=useRouter(),d=async()=>{if(localStorage.getItem("remember")){let r=JSON.parse(localStorage.getItem("remember"));s.value=r.rememberPassword,r.rememberPassword&&(a.value=r.username,n.value=r.password,s.value=r.rememberPassword)}};[e,l]=withAsyncContext(()=>d()),await e,l();const h=()=>{if(a.value.replace(" ","")===""||a.value.replace(" ","")===""){ElMessage.error("用户名或密码为空，请重新输入");return}s.value?localStorage.setItem("remember",JSON.stringify({rememberPassword:s.value,username:a.value,password:n.value})):localStorage.removeItem("remember"),o.value=!o.value,doLogin(a.value,n.value).then(r=>{if(o.value=!o.value,r.msg==1){ElMessage.error(r.infor);return}else ElMessage({message:"登录成功！欢迎你-"+a.value,type:"success"}),c.push({name:"home"})})};return(r,u)=>(openBlock(),createElementBlock("div",_hoisted_1$7,[createBaseVNode("div",_hoisted_2$6,[createBaseVNode("div",_hoisted_3$4,[withDirectives(createBaseVNode("input",{class:"py-2 px-1 w-full bg-transparent border-b focus:border-weather-secondary focus:outline-none focus:shadow-[0px_1px_0_0_#004E71]",type:"text",placeholder:"请输入用户名","onUpdate:modelValue":u[0]||(u[0]=m=>isRef(a)?a.value=m:null)},null,512),[[vModelText,unref(a)]]),withDirectives(createBaseVNode("input",{class:"py-2 px-1 w-full bg-transparent border-b focus:border-weather-secondary focus:outline-none focus:shadow-[0px_1px_0_0_#004E71]",type:"password",placeholder:"请输入密码","onUpdate:modelValue":u[1]||(u[1]=m=>isRef(n)?n.value=m:null)},null,512),[[vModelText,unref(n)]]),createBaseVNode("div",_hoisted_4$4,[withDirectives(createBaseVNode("input",{id:"rem",class:"py-2 px-1 w-full bg-transparent border-b focus:border-weather-secondary",type:"checkbox","onUpdate:modelValue":u[2]||(u[2]=m=>isRef(s)?s.value=m:null)},null,512),[[vModelCheckbox,unref(s)]]),_hoisted_5$4]),createBaseVNode("input",{class:"text-white py-2 px-1 w-full rounded-lg bg-weather-secondary border-b focus:bg-weather-primary focus:shadow-[0px_1px_0_0_#004E71]",type:"button",value:"登录",onClick:h,disabled:unref(o)},null,8,_hoisted_6$3)])])]))}},_sfc_main$9={__name:"LoginView",setup(t){return(e,l)=>(openBlock(),createElementBlock("div",null,[(openBlock(),createBlock(Suspense,null,{default:withCtx(()=>[createVNode(_sfc_main$a)]),fallback:withCtx(()=>[createVNode(_sfc_main$c)]),_:1}))]))}},bigLookImg=t=>{ElMessageBox.alert(`<${t.target.localName} ${t.target.localName=="video"?"controls autoplay":""} onerror="${t.target.localName=="img"?"this.src='unknown.jpeg'":"this.poster='unknown.jpeg'"} " src="${t.target.src}" />`,"放大查看",{dangerouslyUseHTMLString:!0,center:!0})},getInfoFromIdcard=t=>{if(/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(t)){var l=t.substring(6,14),s=t.substring(16,17),o=s%2==1?"男":"女",a=l.substring(0,4);let n={sex:"未知",age:"未知"};return n.sex=o,n.age=new Date().getFullYear()-a,n}else return!1},_hoisted_1$6=["src"],_hoisted_2$5=["src","poster"],_sfc_main$8={__name:"Image",props:{img_src:{type:Object,default:{path:"unknown.jpeg"}},class_val:{type:String,default:"w-full h-24 object-cover rounded-md"},f_type:{type:String,default:"image"}},emits:["bigLookImg"],setup(t){const e=t,l=a=>{console.log(a.target.play())},s=' this.src=" unknown.jpeg " ',o=' this.poster=" unknown.jpeg " ';return(a,n)=>e.f_type=="image"?(openBlock(),createElementBlock("img",{key:0,class:normalizeClass(e.class_val),src:e.img_src.path?e.img_src.path:"unknown.jpeg",onClick:n[0]||(n[0]=(...c)=>unref(bigLookImg)&&unref(bigLookImg)(...c)),onerror:s},null,10,_hoisted_1$6)):(openBlock(),createElementBlock("video",{key:1,onCanplay:l,class:normalizeClass(e.class_val),src:e.img_src.path,onClick:n[1]||(n[1]=(...c)=>unref(bigLookImg)&&unref(bigLookImg)(...c)),onerror:o,poster:"hasVideo.jpeg"},null,42,_hoisted_2$5))}},_hoisted_1$5=["value"],_sfc_main$7=defineComponent({__name:"ImageView",props:{f_type:{type:String,default:"image"},widthPro:{type:String,default:""},imgClass:{type:String,default:"w-full h-24 object-cover rounded-md"},src:{type:Object||Boolean,default:null},dec:{type:String,default:"上传图片"},showUpload:{type:Boolean,default:!1},capture:{type:String,default:"default"},type:{type:String,default:"zp"},fatherid:{type:String,default:0},itemid:{type:Number,default:0},t:{type:Number,default:0},it_name:{type:String,default:""}},setup(t){const e=t,l=useRouter(),s=ref(0),o=ref(""),a=ref(!1),n=computed(()=>(e.src.hasOwnProperty(e.type)||(e.src[e.type]={path:""}),e.type!="fwzp"&&e.type!="fwsp"?e.src.hasOwnProperty(e.type)?e.src[e.type]:{path:""}:e.src.hasOwnProperty(e.type)&&e.src[e.type].hasOwnProperty(e.itemid)&&e.src[e.type][e.itemid].hasOwnProperty(e.t)?e.src[e.type][e.itemid][e.t]:{path:""})),c=computed(()=>o.value=="success"?!0:n.value.hasOwnProperty("path")&&n.value.path.indexOf("uploadss")>-1);onMounted(()=>{e.capture==="capture_img"&&document.getElementsByName("capture_img").forEach(p=>{p.setAttribute("capture","camera")})});const d=i=>i.type==="image/jpeg"||i.type==="image/png"?new Promise(g=>{imageConversionExports.compressAccurately(i,250).then(v=>{g(v)})}):(ElMessage.error("上传图片只能是 JPG 或 PNG 格式!"),!1),h=i=>{s.value=parseInt((i.loaded/i.total*100).toFixed(0))},r=async()=>{await ElMessageBox.confirm(`您确认要删除【${e.it_name}】的第${e.t+1}张照片吗?`,"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{let i=await withLoading(delImage)(_.value,l);if(i.msg==1){ElMessage.error(i.infor);return}else ElMessage.success(i.infor),n.value.path=""}catch(i){ElMessage.error(i.message)}}).catch(()=>{})},u=ref(),m=()=>{a.value=!0,u.value.submit()},f=async(i,p)=>{await withLoading(()=>{if(i.status==="ready")console.log(i,p),m();else if(i.status==="fail")a.value=!1,ElMessage.error("上传失败"),o.value="error";else if(i.status==="success"){a.value=!1;let g=i.response;g.msg==2?(ElMessage.success(g.infor),o.value="success",e.type!="fwzp"&&e.type!="fwsp"?e.src[e.type]={path:i.url}:e.src[e.type][e.itemid][e.t]={path:i.url},e.f_type=="video"):g.msg==1?(a.value=!1,ElMessage.error(g.infor)):(a.value=!1,ElMessage.error(g.msg),g.msg==="登录信息已失效，请重新登录"&&l.push({name:"login"}))}else a.value=!1,console.log(i.status,i)})()},_=ref({father_id:e.fatherid,item_id:e.itemid,t:e.t,f_type:e.type,name:e.capture});return(i,p)=>{const g=ElProgress,v=vLoading;return withDirectives((openBlock(),createElementBlock("div",{class:normalizeClass(`grid grid-flow-row grid-cols-none ${t.widthPro} items-center text-white`)},[createVNode(_sfc_main$8,{class_val:e.imgClass,img_src:unref(n),f_type:e.f_type},null,8,["class_val","img_src","f_type"]),(openBlock(),createBlock(Teleport,{to:"body"},[a.value?(openBlock(),createBlock(g,{key:0,style:{position:"fixed !important"},class:"w-full z-[9999999] top-[53%]","stroke-width":20,percentage:s.value,status:o.value,"text-inside":!0,duration:5},null,8,["percentage","status"])):createCommentVNode("",!0)])),t.showUpload?(openBlock(),createBlock(unref(ElUpload),{key:0,name:t.capture,"on-progress":h,"on-change":f,"before-upload":d,action:"/api/upload/image",ref_key:"upload",ref:u,"list-type":"picture-card","auto-upload":!1,"show-file-list":!1,data:_.value,accept:e.f_type=="image"?"image/*":"video/*",class:"w-full text-center"},{default:withCtx(()=>[createBaseVNode("input",{type:"button",style:{color:"white !important"},class:"w-full rounded-b-md border border-blue-500 bg-btn_color",value:t.dec},null,8,_hoisted_1$5),unref(c)?(openBlock(),createElementBlock("input",{key:0,type:"button",style:{color:"white !important"},onClick:p[0]||(p[0]=withModifiers(w=>r(),["stop"])),class:"w-full rounded-b-md border border-blue-500 bg-btn_color",value:"删除"})):createCommentVNode("",!0)]),_:1},8,["name","data","accept"])):createCommentVNode("",!0)],2)),[[v,a.value,void 0,{fullscreen:!0,lock:!0}]])}}}),_hoisted_1$4={class:"w-full"},_hoisted_2$4=["value"],_sfc_main$6={__name:"confirm",props:{value:{type:String,default:"提交采集信息"},cssStyle:{type:String,default:""}},emits:["submit"],setup(t){return(e,l)=>(openBlock(),createElementBlock("div",_hoisted_1$4,[createBaseVNode("input",{type:"button",value:t.value,class:normalizeClass(`${t.cssStyle} w-full  mb-4 py-2 bg-green-600 text-white rounded-b-xl`),onClick:l[0]||(l[0]=s=>e.$emit("submit"))},null,10,_hoisted_2$4)]))}},_hoisted_1$3={class:"flex flex-col flex-1 items-center bg-weather-primary text-black"},_hoisted_2$3={class:"grid grid-flow-col grid-cols-none items-center py-2 w-full px-2"},_hoisted_3$3={class:"grid grid-flow-row grid-rows-3 text-left items-center px-2 py-2"},_hoisted_4$3={class:"text-xl mb-0.5 font-bold"},_hoisted_5$3={class:"text-base mb-0.5"},_hoisted_6$2={class:"text-base mb-0.5"},_hoisted_7$2={class:"w-full px-2 rounded-md text-black border border-gray-400 border-opacity-30"},_hoisted_8$2={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center font-light"},_hoisted_9$2=createBaseVNode("label",null,"家庭地址:",-1),_hoisted_10$2={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center font-light"},_hoisted_11$2=createBaseVNode("label",null,"身份证号:",-1),_hoisted_12$2={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center font-light"},_hoisted_13$2=createBaseVNode("label",null,"本人电话:",-1),_hoisted_14$1={class:"grid grid-cols-2 grid-rows-none items-center font-light"},_hoisted_15$1={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center"},_hoisted_16$1=createBaseVNode("label",null,"性别:",-1),_hoisted_17$1={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center"},_hoisted_18$1=createBaseVNode("label",null,"年龄:",-1),_hoisted_19$1={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center"},_hoisted_20$1=createBaseVNode("label",null,"监护人姓名:",-1),_hoisted_21$1={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center"},_hoisted_22$1=createBaseVNode("label",null,"电话:",-1),_hoisted_23$1={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center"},_hoisted_24$1=createBaseVNode("label",null,"村干部姓名:",-1),_hoisted_25$1={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center"},_hoisted_26$1=createBaseVNode("label",null,"电话:",-1),_hoisted_27$1={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center font-light"},_hoisted_28$1=createBaseVNode("label",null,"生活状态:",-1),_hoisted_29$1={class:"mx-2"},_hoisted_30$1=createBaseVNode("h2",{class:"mb-4"},"自愿放弃协议:",-1),_hoisted_31$1={class:normalizeClass("w-full overflow-x-scroll flex flex-row text-center gap-[1%]")},_hoisted_32$1={class:normalizeClass("w-full overflow-x-scroll flex flex-row text-center gap-[1%] my-2")},_hoisted_33$1={class:"max-w-screen-md w-full py-2"},_hoisted_34$1={class:"mx-4"},_hoisted_35$1={class:normalizeClass("w-full overflow-x-scroll flex flex-row text-center gap-[1%]")},_sfc_main$5={__name:"AsyncCollectionView",async setup(__props){let __temp,__restore;const class_read=ref({noread:" py-2 px-1 w-full bg-transparent border-b focus:border-weather-secondary focus:outline-none shadow-[0px_1px_0_0_#5ec19d] focus:shadow-[0px_1px_0_0_#004E71]",read:" py-2 px-1 w-full bg-transparent border-opacity-20 border border-gray-500 rounded-lg"});onMounted(()=>{document.scrollingElement.scrollTop=0});const cs=()=>{console.log(oldmanInfo.value)},route=useRoute(),odmId=route.params.id===void 0?664:route.params.id,router=useRouter(),getOptions=async()=>{let t=await getSysConfig({option_key:"live_state,zyfq_zp,cj_type",data_type:"json"},router);return t.msg==1?(ElMessage.error(t.infor),{list:{option_value:[{label:"正常",value:"正常"}]}}):t},options=ref({});options.value=([__temp,__restore]=withAsyncContext(()=>getOptions()),__temp=await __temp,__restore(),__temp),options.value.list.cj_type.option_value=eval("("+options.value.list.cj_type.option_value+")"),options.value.list.zyfq_zp.option_value=eval("("+options.value.list.zyfq_zp.option_value+")"),options.value.list.live_state.option_value=eval("("+options.value.list.live_state.option_value+")");const getOldmanInfo=async t=>{try{let e=await withLoading(getOldmanInfo_collection)(t,router);if(e.msg=="1"){ElMessage.error(e.infor);return}if(e.list.idcard){let l=getInfoFromIdcard(e.list.idcard);e.list.age=l.age,e.list.sex=l.sex}return e}catch(e){ElMessage.error(e.message)}},oldmanInfo=ref(null);oldmanInfo.value=([__temp,__restore]=withAsyncContext(()=>getOldmanInfo(odmId)),__temp=await __temp,__restore(),__temp);const submit_info=async()=>{await ElMessageBox.confirm("您确认要提交信息吗?","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{let t=await withLoading(saveOldmanInfo_collection)(oldmanInfo.value.list,router);if(t.msg===1||t.hasOwnProperty("errorCode")){ElMessage.error(t.infor);return}else ElMessage.success(t.infor),router.push({name:"home"})}catch(t){ElMessage.error(t.message)}}).catch(()=>{})};return(t,e)=>{const l=ElOption,s=ElSelect;return openBlock(),createElementBlock("div",_hoisted_1$3,[createBaseVNode("div",_hoisted_2$3,[createVNode(_sfc_main$7,{class:"text-sm","width-pro":"w-24",src:oldmanInfo.value.list.image,dec:"上传头像","show-upload":!0,fatherid:unref(odmId),type:"zp",it_name:"上传头像"},null,8,["src","fatherid"]),createBaseVNode("div",_hoisted_3$3,[createBaseVNode("p",_hoisted_4$3,"姓名："+toDisplayString(oldmanInfo.value.list.name),1),createBaseVNode("p",_hoisted_5$3,"采集审核状态："+toDisplayString(oldmanInfo.value.list.cj===null?"待审核":oldmanInfo.value.list.cj),1),createBaseVNode("p",_hoisted_6$2,"采集时间："+toDisplayString(oldmanInfo.value.list.collect_time===null?"未采集":oldmanInfo.value.list.collect_time),1)])]),createBaseVNode("div",_hoisted_7$2,[createBaseVNode("div",_hoisted_8$2,[_hoisted_9$2,withDirectives(createBaseVNode("input",{type:"text","onUpdate:modelValue":e[0]||(e[0]=o=>oldmanInfo.value.list.address=o),readonly:"",class:normalizeClass(class_read.value.read)},null,2),[[vModelText,oldmanInfo.value.list.address]])]),createBaseVNode("div",_hoisted_10$2,[_hoisted_11$2,withDirectives(createBaseVNode("input",{type:"text","onUpdate:modelValue":e[1]||(e[1]=o=>oldmanInfo.value.list.idcard=o),readonly:"",class:normalizeClass(class_read.value.read)},null,2),[[vModelText,oldmanInfo.value.list.idcard]])]),createBaseVNode("div",_hoisted_12$2,[_hoisted_13$2,withDirectives(createBaseVNode("input",{type:"text","onUpdate:modelValue":e[2]||(e[2]=o=>oldmanInfo.value.list.self_phone=o),class:normalizeClass(class_read.value.noread)},null,2),[[vModelText,oldmanInfo.value.list.self_phone]])]),createBaseVNode("div",_hoisted_14$1,[createBaseVNode("div",_hoisted_15$1,[_hoisted_16$1,withDirectives(createBaseVNode("input",{type:"text","onUpdate:modelValue":e[3]||(e[3]=o=>oldmanInfo.value.list.sex=o),readonly:"",class:normalizeClass(class_read.value.read)},null,2),[[vModelText,oldmanInfo.value.list.sex]])]),createBaseVNode("div",_hoisted_17$1,[_hoisted_18$1,withDirectives(createBaseVNode("input",{type:"number","onUpdate:modelValue":e[4]||(e[4]=o=>oldmanInfo.value.list.age=o),readonly:"",class:normalizeClass(class_read.value.read)},null,2),[[vModelText,oldmanInfo.value.list.age]])]),createBaseVNode("div",_hoisted_19$1,[_hoisted_20$1,withDirectives(createBaseVNode("input",{type:"text","onUpdate:modelValue":e[5]||(e[5]=o=>oldmanInfo.value.list.relation_name=o),class:normalizeClass(class_read.value.noread)},null,2),[[vModelText,oldmanInfo.value.list.relation_name]])]),createBaseVNode("div",_hoisted_21$1,[_hoisted_22$1,withDirectives(createBaseVNode("input",{type:"text","onUpdate:modelValue":e[6]||(e[6]=o=>oldmanInfo.value.list.relation_phone=o),class:normalizeClass(class_read.value.noread)},null,2),[[vModelText,oldmanInfo.value.list.relation_phone]])]),createBaseVNode("div",_hoisted_23$1,[_hoisted_24$1,withDirectives(createBaseVNode("input",{type:"text","onUpdate:modelValue":e[7]||(e[7]=o=>oldmanInfo.value.list.cadre_name=o),class:normalizeClass(class_read.value.noread)},null,2),[[vModelText,oldmanInfo.value.list.cadre_name]])]),createBaseVNode("div",_hoisted_25$1,[_hoisted_26$1,withDirectives(createBaseVNode("input",{type:"text","onUpdate:modelValue":e[8]||(e[8]=o=>oldmanInfo.value.list.cadre_phone=o),class:normalizeClass(class_read.value.noread)},null,2),[[vModelText,oldmanInfo.value.list.cadre_phone]])])]),createBaseVNode("div",_hoisted_27$1,[_hoisted_28$1,createVNode(s,{modelValue:oldmanInfo.value.list.live_state,"onUpdate:modelValue":e[9]||(e[9]=o=>oldmanInfo.value.list.live_state=o),class:"m-2 w-full",placeholder:"请选择生活状态",size:"large"},{default:withCtx(()=>[(openBlock(!0),createElementBlock(Fragment,null,renderList(options.value.list.live_state.option_value.value,o=>(openBlock(),createBlock(l,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),withDirectives(createBaseVNode("div",_hoisted_29$1,[_hoisted_30$1,createBaseVNode("div",_hoisted_31$1,[(openBlock(!0),createElementBlock(Fragment,null,renderList(options.value.list.zyfq_zp.option_value.image,(o,a)=>(openBlock(),createBlock(_sfc_main$7,{src:oldmanInfo.value.list.image,"img-class":"w-full h-24 object-cover rounded-t-md ","width-pro":"min-w-[24%] flex-1",fatherid:unref(odmId),key:a,capture:"capture_img",type:o.value,dec:o.label,"show-upload":!0,f_type:"image"},null,8,["src","fatherid","type","dec"]))),128))]),createBaseVNode("div",_hoisted_32$1,[(openBlock(!0),createElementBlock(Fragment,null,renderList(options.value.list.zyfq_zp.option_value.video,(o,a)=>(openBlock(),createBlock(_sfc_main$7,{src:oldmanInfo.value.list.image,"img-class":"w-full h-32 object-cover rounded-t-md ","width-pro":"min-w-[24%] flex-1",fatherid:unref(odmId),key:a,capture:"capture_img",type:o.value,dec:o.label,"show-upload":!0,f_type:"video"},null,8,["src","fatherid","type","dec"]))),128))])],512),[[vShow,oldmanInfo.value.list.live_state=="自愿放弃服务"]])]),createBaseVNode("div",_hoisted_33$1,[createBaseVNode("div",_hoisted_34$1,[createBaseVNode("h2",{onClick:cs,class:"mb-4"},"采集照片"),createBaseVNode("div",_hoisted_35$1,[(openBlock(!0),createElementBlock(Fragment,null,renderList(options.value.list.cj_type.option_value.value,(o,a)=>(openBlock(),createBlock(_sfc_main$7,{"width-pro":"min-w-[24%] flex-1",fatherid:unref(odmId),key:a,capture:"capture_img",src:oldmanInfo.value.list.image,type:o.value,dec:o.label,"show-upload":!0,it_name:o.label},null,8,["fatherid","src","type","dec","it_name"]))),128))])]),createVNode(_sfc_main$6,{value:"提交采集信息",onSubmit:submit_info,class:"my-6"})])])}}},_hoisted_1$2={class:"flex flex-col flex-1 items-center bg-weather-primary text-black"},_hoisted_2$2={class:"grid grid-flow-col grid-cols-none items-center py-2 w-full px-2"},_hoisted_3$2={class:"grid grid-flow-row grid-rows-3 text-left items-center px-2 py-2"},_hoisted_4$2={class:"text-xl mb-0.5 font-bold"},_hoisted_5$2={class:"text-base mb-0.5"},_hoisted_6$1={class:"text-base mb-0.5"},_hoisted_7$1={class:"w-full px-2 rounded-md text-black border border-gray-400 border-opacity-30"},_hoisted_8$1={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center font-light"},_hoisted_9$1=createBaseVNode("label",null,"家庭地址:",-1),_hoisted_10$1={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center font-light"},_hoisted_11$1=createBaseVNode("label",null,"身份证号:",-1),_hoisted_12$1={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center font-light"},_hoisted_13$1=createBaseVNode("label",null,"本人电话:",-1),_hoisted_14={class:"grid grid-cols-2 grid-rows-none items-center font-light"},_hoisted_15={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center"},_hoisted_16=createBaseVNode("label",null,"性别:",-1),_hoisted_17={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center"},_hoisted_18=createBaseVNode("label",null,"年龄:",-1),_hoisted_19={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center"},_hoisted_20=createBaseVNode("label",null,"监护人姓名:",-1),_hoisted_21={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center"},_hoisted_22=createBaseVNode("label",null,"电话:",-1),_hoisted_23={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center"},_hoisted_24=createBaseVNode("label",null,"村干部姓名:",-1),_hoisted_25={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center"},_hoisted_26=createBaseVNode("label",null,"电话:",-1),_hoisted_27={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none items-center font-light"},_hoisted_28=createBaseVNode("label",null,"生活状态:",-1),_hoisted_29={class:"mx-2"},_hoisted_30=createBaseVNode("h2",{class:"mb-4"},"自愿放弃协议:",-1),_hoisted_31={class:normalizeClass("w-full overflow-x-scroll flex flex-row text-center gap-[1%]")},_hoisted_32={class:normalizeClass("w-full overflow-x-scroll flex flex-row text-center gap-[1%] my-2")},_hoisted_33={class:"max-w-screen-md w-full py-2"},_hoisted_34={class:"mx-4"},_hoisted_35=createBaseVNode("h2",{class:"mb-4"},"采集照片",-1),_hoisted_36={class:normalizeClass("w-full overflow-x-scroll flex flex-row text-center gap-[1%]")},_sfc_main$4={__name:"SerivcesInfoShow",props:{oldmanInfo:{type:Object,default:{}},options:{type:Object,default:{}},odmId:{type:String,default:"0"},btnText:{type:String,default:"确认信息，开始服务"},isShow:{type:Boolean,default:!1},cssStyle_infoShow:{type:String,default:""}},emits:["submit_info_show","submit_info_save"],setup(t){const e=ref({noread:" py-2 px-1 w-full bg-transparent border-b focus:border-weather-secondary focus:outline-none shadow-[0px_1px_0_0_#5ec19d] focus:shadow-[0px_1px_0_0_#004E71]",read:" py-2 px-1 w-full bg-transparent border-opacity-20 border border-gray-500 rounded-lg"});return(l,s)=>{const o=ElSelect;return openBlock(),createElementBlock("div",_hoisted_1$2,[createBaseVNode("div",_hoisted_2$2,[createVNode(_sfc_main$7,{"width-pro":"w-24",src:t.oldmanInfo.list.image,dec:"上传头像","show-upload":!1,fatherid:t.odmId,type:"zp"},null,8,["src","fatherid"]),createBaseVNode("div",_hoisted_3$2,[createBaseVNode("p",_hoisted_4$2,"姓名："+toDisplayString(t.oldmanInfo.list.name),1),createBaseVNode("p",_hoisted_5$2,"采集状态："+toDisplayString(t.oldmanInfo.list.cj===null?"未采集":t.oldmanInfo.list.cj),1),createBaseVNode("p",_hoisted_6$1,"采集时间："+toDisplayString(t.oldmanInfo.list.collect_time===null?"未采集":t.oldmanInfo.list.collect_time),1)])]),createVNode(Transition,{duration:500,"enter-active-class":"transform transition duration-300 ease-custom","enter-class":"-translate-y-1/2 scale-y-0 opacity-0","enter-to-class":"translate-y-0 scale-y-100 opacity-100","leave-active-class":"transform transition duration-300 ease-custom","leave-class":"translate-y-0 scale-y-100 opacity-100","leave-to-class":"-translate-y-1/2 scale-y-0 opacity-0"},{default:withCtx(()=>[withDirectives(createBaseVNode("div",null,[createBaseVNode("div",_hoisted_7$1,[createBaseVNode("div",_hoisted_8$1,[_hoisted_9$1,withDirectives(createBaseVNode("input",{type:"text","onUpdate:modelValue":s[0]||(s[0]=a=>t.oldmanInfo.list.address=a),readonly:"",class:normalizeClass(e.value.read)},null,2),[[vModelText,t.oldmanInfo.list.address]])]),createBaseVNode("div",_hoisted_10$1,[_hoisted_11$1,withDirectives(createBaseVNode("input",{type:"text","onUpdate:modelValue":s[1]||(s[1]=a=>t.oldmanInfo.list.idcard=a),readonly:"",class:normalizeClass(e.value.read)},null,2),[[vModelText,t.oldmanInfo.list.idcard]])]),createBaseVNode("div",_hoisted_12$1,[_hoisted_13$1,withDirectives(createBaseVNode("input",{type:"text","onUpdate:modelValue":s[2]||(s[2]=a=>t.oldmanInfo.list.self_phone=a),readonly:"",class:normalizeClass(e.value.read)},null,2),[[vModelText,t.oldmanInfo.list.self_phone]])]),createBaseVNode("div",_hoisted_14,[createBaseVNode("div",_hoisted_15,[_hoisted_16,withDirectives(createBaseVNode("input",{type:"text","onUpdate:modelValue":s[3]||(s[3]=a=>t.oldmanInfo.list.sex=a),readonly:"",class:normalizeClass(e.value.read)},null,2),[[vModelText,t.oldmanInfo.list.sex]])]),createBaseVNode("div",_hoisted_17,[_hoisted_18,withDirectives(createBaseVNode("input",{type:"number","onUpdate:modelValue":s[4]||(s[4]=a=>t.oldmanInfo.list.age=a),readonly:"",class:normalizeClass(e.value.read)},null,2),[[vModelText,t.oldmanInfo.list.age]])]),createBaseVNode("div",_hoisted_19,[_hoisted_20,withDirectives(createBaseVNode("input",{type:"text","onUpdate:modelValue":s[5]||(s[5]=a=>t.oldmanInfo.list.relation_name=a),readonly:"",class:normalizeClass(e.value.read)},null,2),[[vModelText,t.oldmanInfo.list.relation_name]])]),createBaseVNode("div",_hoisted_21,[_hoisted_22,withDirectives(createBaseVNode("input",{type:"text","onUpdate:modelValue":s[6]||(s[6]=a=>t.oldmanInfo.list.relation_phone=a),readonly:"",class:normalizeClass(e.value.read)},null,2),[[vModelText,t.oldmanInfo.list.relation_phone]])]),createBaseVNode("div",_hoisted_23,[_hoisted_24,withDirectives(createBaseVNode("input",{type:"text","onUpdate:modelValue":s[7]||(s[7]=a=>t.oldmanInfo.list.cadre_name=a),readonly:"",class:normalizeClass(e.value.read)},null,2),[[vModelText,t.oldmanInfo.list.cadre_name]])]),createBaseVNode("div",_hoisted_25,[_hoisted_26,withDirectives(createBaseVNode("input",{type:"text","onUpdate:modelValue":s[8]||(s[8]=a=>t.oldmanInfo.list.cadre_phone=a),readonly:"",class:normalizeClass(e.value.read)},null,2),[[vModelText,t.oldmanInfo.list.cadre_phone]])])]),createBaseVNode("div",_hoisted_27,[_hoisted_28,createVNode(o,{modelValue:t.oldmanInfo.list.live_state,"onUpdate:modelValue":s[9]||(s[9]=a=>t.oldmanInfo.list.live_state=a),disabled:"",class:"m-2 w-full px-2",placeholder:"请选择生活状态",size:"large"},null,8,["modelValue"])]),withDirectives(createBaseVNode("div",_hoisted_29,[_hoisted_30,createBaseVNode("div",_hoisted_31,[(openBlock(!0),createElementBlock(Fragment,null,renderList(t.options.list.zyfq_zp.option_value.image,(a,n)=>(openBlock(),createBlock(_sfc_main$7,{src:t.oldmanInfo.list.image,"img-class":"w-full h-24 object-cover rounded-t-md ","width-pro":"min-w-[24%] flex-1",fatherid:t.odmId,key:n,capture:"capture_img",type:a.value,dec:a.label,"show-upload":!0,f_type:"image"},null,8,["src","fatherid","type","dec"]))),128))]),createBaseVNode("div",_hoisted_32,[(openBlock(!0),createElementBlock(Fragment,null,renderList(t.options.list.zyfq_zp.option_value.video,(a,n)=>(openBlock(),createBlock(_sfc_main$7,{src:t.oldmanInfo.list.image,"img-class":"w-full h-32 object-cover rounded-t-md ","width-pro":"min-w-[24%] flex-1",fatherid:t.odmId,key:n,capture:"capture_img",type:a.value,dec:a.label,"show-upload":!0,f_type:"video"},null,8,["src","fatherid","type","dec"]))),128))])],512),[[vShow,t.oldmanInfo.list.live_state=="自愿放弃服务"]])]),createBaseVNode("div",_hoisted_33,[createBaseVNode("div",_hoisted_34,[_hoisted_35,createBaseVNode("div",_hoisted_36,[(openBlock(!0),createElementBlock(Fragment,null,renderList(t.options.list.cj_type.option_value.value,(a,n)=>(openBlock(),createBlock(_sfc_main$7,{"width-pro":"min-w-[24%] flex-1",fatherid:t.odmId,key:n,capture:"capture_img",src:t.oldmanInfo.list.image,type:a.value,dec:a.label,"show-upload":!1},null,8,["fatherid","src","type","dec"]))),128))])])])],512),[[vShow,t.isShow]])]),_:1}),createVNode(_sfc_main$6,{"css-style":t.cssStyle_infoShow,value:t.btnText,onSubmit:s[10]||(s[10]=a=>l.$emit("submit_info_show")),class:"my-2"},null,8,["css-style","value"])])}}},_sfc_main$3={__name:"AsyncServicesView",async setup(__props){let __temp,__restore;onMounted(()=>{document.scrollingElement.scrollTop=0});const route=useRoute(),odmId=route.params.id===void 0?664:route.params.id,router=useRouter(),getOptions=async()=>{let t=await getSysConfig({option_key:"live_state,zyfq_zp,cj_type",data_type:"json"},router);return t.msg==1?(ElMessage.error(t.infor),{list:{option_value:[{label:"正常",value:"正常"}]}}):t},options=ref({});options.value=([__temp,__restore]=withAsyncContext(()=>getOptions()),__temp=await __temp,__restore(),__temp),options.value.list.cj_type.option_value=eval("("+options.value.list.cj_type.option_value+")"),options.value.list.zyfq_zp.option_value=eval("("+options.value.list.zyfq_zp.option_value+")"),options.value.list.live_state.option_value=eval("("+options.value.list.live_state.option_value+")");const getOldmanInfo=async t=>{try{let e=await withLoading(getOldmanInfo_collection)(t,router);if(e.msg=="1"){ElMessage.error(e.infor);return}if(e.list.idcard){let l=getInfoFromIdcard(e.list.idcard);e.list.age=l.age,e.list.sex=l.sex}return e}catch(e){ElMessage.error(e.message)}},oldmanInfo=ref(null);oldmanInfo.value=([__temp,__restore]=withAsyncContext(()=>getOldmanInfo(odmId)),__temp=await __temp,__restore(),__temp);const submit_info=async()=>{await ElMessageBox.confirm(`您确认要对【${oldmanInfo.value.list.name}】开始进行服务吗?`,"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{let t=await withLoading(order_start)({id:oldmanInfo.value.list.id,start_xy:"34.64513,115.46678"},router);if(t.msg==1){ElMessage.error(t.infor);return}else ElMessage.success(t.infor),router.push({name:"collectionView",params:{id:oldmanInfo.value.list.id,type:"fw"},query:{rid:t.rid,state:"zz",name:"正在服务中"}})}catch(t){ElMessage.error(t.message)}}).catch(()=>{})};return(t,e)=>(openBlock(),createElementBlock(Fragment,null,[createVNode(_sfc_main$m,{name:"服务",lujing:unref(route).query.name},null,8,["lujing"]),createVNode(_sfc_main$4,{"is-show":!0,"btn-text":"确认信息，开始服务","odm-id":unref(odmId),"oldman-info":oldmanInfo.value,onSubmit_info_show:submit_info,options:options.value},null,8,["odm-id","oldman-info","options"])],64))}},_hoisted_1$1={class:"bg-white w-full text-center flex flex-col sticky top-[72px] z-10 items-center"},_hoisted_2$1={class:"flex-1"},_hoisted_3$1={class:"flex-1"},_hoisted_4$1={class:"flex-1"},_hoisted_5$1=createBaseVNode("hr",null,null,-1),_hoisted_6={class:"w-full px-2 py-2 grid grid-flow-col grid-cols-none text-center text-white sticky top-[144px] bg-white"},_hoisted_7=["onClick"],_hoisted_8={class:"w-full px-2 grid grid-flow-row grid-row-none text-center text-white"},_hoisted_9={class:"my-2"},_hoisted_10={class:normalizeClass("w-full overflow-x-scroll flex flex-row text-center gap-[1%] px-2 pb-1")},_hoisted_11={key:0,class:"w-full text-blue-500"},_hoisted_12=["placeholder"],_hoisted_13={class:normalizeClass("w-full overflow-x-scroll flex flex-row text-center gap-[1%] px-2 pb-1")},_sfc_main$2={__name:"AsyncServicesNowView",async setup(__props){let __temp,__restore;onMounted(()=>{document.scrollingElement.scrollTop=0});const route=useRoute(),now_state=ref(""),odmId=route.params.id,router=useRouter(),isshow=ref(!1),getOptions=async()=>{let t=await getSysConfig({option_key:"live_state,zyfq_zp,cj_type",data_type:"json"},router);return t.msg==1?(ElMessage.error(t.infor),{list:{option_value:[{label:"正常",value:"正常"}]}}):t},options=ref({});options.value=([__temp,__restore]=withAsyncContext(()=>getOptions()),__temp=await __temp,__restore(),__temp),options.value.list.cj_type.option_value=eval("("+options.value.list.cj_type.option_value+")"),options.value.list.zyfq_zp.option_value=eval("("+options.value.list.zyfq_zp.option_value+")"),options.value.list.live_state.option_value=eval("("+options.value.list.live_state.option_value+")");const getOldmanInfo=async t=>{try{let e=await withLoading(getOldmanInfo_collection)(t,router);if(e.msg=="1"){ElMessage.error(e.infor);return}if(e.list.idcard){let l=getInfoFromIdcard(e.list.idcard);e.list.age=l.age,e.list.sex=l.sex}return e}catch(e){ElMessage.error(e.message)}},getOrderInfo=async t=>{try{let e=await withLoading(getOrderInfo_services)(t,router);if(e.msg=="1"){ElMessage.error(e.infor);return}return now_state.value=Object.keys(e.list.item.items)[0],e}catch(e){ElMessage.error(e.message)}},oldmanInfo=ref(null);oldmanInfo.value=([__temp,__restore]=withAsyncContext(()=>getOldmanInfo(odmId)),__temp=await __temp,__restore(),__temp);const orderInfo=ref(null);orderInfo.value=([__temp,__restore]=withAsyncContext(()=>getOrderInfo(route.query.rid)),__temp=await __temp,__restore(),__temp);const submit_info=async()=>{await ElMessageBox.confirm(`您确认要对【${oldmanInfo.value.list.name}】的服务进行签退吗?`,"提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{let t=await withLoading(order_end)({id:orderInfo.value.list.id,end_xy:"34.64513,115.46678",all_uids:"1,2"},router);if(t.msg===1||t.hasOwnProperty("errorCode")){ElMessage.error(t.infor);return}else ElMessage.success(t.infor),router.push({name:"services"})}catch(t){ElMessage.error(t.message)}}).catch(()=>{})};return(t,e)=>(openBlock(),createElementBlock(Fragment,null,[createVNode(_sfc_main$m,{name:"服务",lujing:unref(route).query.name},null,8,["lujing"]),createBaseVNode("div",_hoisted_1$1,[createBaseVNode("span",_hoisted_2$1," 签到人员:"+toDisplayString(orderInfo.value.list.uname),1),createBaseVNode("span",_hoisted_3$1," 服务开始时间:"+toDisplayString(orderInfo.value.list.start_time),1),createBaseVNode("span",_hoisted_4$1," 服务结束时间:"+toDisplayString(orderInfo.value.list.end_time),1)]),createVNode(_sfc_main$4,{"is-show":isshow.value,"btn-text":`${isshow.value?"收起":"展开"}待服务人员采集信息`,"odm-id":unref(odmId),"oldman-info":oldmanInfo.value,onSubmit_info_show:e[0]||(e[0]=l=>isshow.value=!isshow.value),options:options.value},null,8,["is-show","btn-text","odm-id","oldman-info","options"]),_hoisted_5$1,createBaseVNode("div",_hoisted_6,[(openBlock(!0),createElementBlock(Fragment,null,renderList(orderInfo.value.list.item.items,(l,s)=>(openBlock(),createElementBlock("div",{onClick:o=>now_state.value=s,class:normalizeClass(`rounded-lg ${s==now_state.value?"bg-cj_bg":"bg-bottom_bg"} mx-1`),key:s},toDisplayString(s),11,_hoisted_7))),128))]),createBaseVNode("div",_hoisted_8,[(openBlock(!0),createElementBlock(Fragment,null,renderList(orderInfo.value.list.item.all_items,(l,s)=>withDirectives((openBlock(),createElementBlock("div",{class:"w-full grid grid-flow-row grid-row-none bg-cj_bg my-2 rounded-lg",key:l.id},[createBaseVNode("h2",_hoisted_9,toDisplayString(l.name),1),createBaseVNode("div",_hoisted_10,[(openBlock(),createElementBlock(Fragment,null,renderList([{type:"fwzp",t:0},{type:"fwzp",t:1},{type:"fwzp",t:2},{type:"fwzp",t:3}],(o,a)=>createVNode(_sfc_main$7,{it_name:l.name,src:orderInfo.value.list.image,"img-class":"w-full h-24 object-cover rounded-t-md ","width-pro":"min-w-[24%] flex-1",fatherid:unref(route).query.rid,key:a,capture:"capture_img",type:o.type,itemid:l.id,t:o.t,dec:`图片${a+1}`,"show-upload":!0,f_type:"image"},null,8,["it_name","src","fatherid","type","itemid","t","dec"])),64))]),l.need_number==1?(openBlock(),createElementBlock("div",_hoisted_11,[createBaseVNode("input",{type:"text",placeholder:l.number_str,class:"w-full border border-blue-500"},null,8,_hoisted_12)])):createCommentVNode("",!0)])),[[vShow,l.menu_type==now_state.value]])),128))]),createBaseVNode("div",_hoisted_13,[(openBlock(),createElementBlock(Fragment,null,renderList([{type:"fwsp",t:0},{type:"fwsp",t:1}],(l,s)=>createVNode(_sfc_main$7,{it_name:"服务视频",src:orderInfo.value.list.image,"img-class":"w-full h-24 object-cover rounded-t-md ","width-pro":"min-w-[50%] flex-1",fatherid:unref(route).query.rid,key:s,capture:"capture_img",type:l.type,itemid:0,t:l.t,dec:`服务视频${s+1}`,"show-upload":!0,f_type:"video"},null,8,["src","fatherid","type","t","dec"])),64))]),unref(route).query.state=="zz"?(openBlock(),createBlock(_sfc_main$6,{key:0,value:"服务签退",onSubmit:submit_info,class:"my-6"})):createCommentVNode("",!0)],64))}},_sfc_main$1={__name:"CollectionView",setup(t){const e=useRoute();return(l,s)=>(openBlock(),createElementBlock("div",null,[(openBlock(),createBlock(Suspense,null,{default:withCtx(()=>[unref(e).params.type=="cj"?(openBlock(),createBlock(_sfc_main$5,{key:0})):unref(e).params.type=="fw"&&unref(e).query.state=="jr"?(openBlock(),createBlock(_sfc_main$3,{key:1})):(openBlock(),createBlock(_sfc_main$2,{key:2}))]),fallback:withCtx(()=>[createVNode(_sfc_main$c)]),_:1}))]))}},_hoisted_1={class:"px-2 w-full text-black pb-12 pt-2"},_hoisted_2={class:"pb-12 mb-2 relative"},_hoisted_3={class:"flex flex-col gap-4"},_hoisted_4={class:"grid grid-flow-col grid-cols-3"},_hoisted_5=["value","onClick"],_sfc_main={__name:"ServiceView",setup(t){const e=ref([]),l=ref(null),s=useRouter(),o=async _=>{n.value.val=_,await withLoading(c)(l.value.townSelect,l.value.villageSelect)},a=ref([{name:"正在服务中",state:"zz"},{name:"今日待服务",state:"jr"},{name:"服务记录",state:"fwjl"}]),n=ref({val:{state:"jr",name:"今日待服务"}}),c=async(_,i)=>{try{await withLoading(()=>new Promise((p,g)=>{p(getOldmanList_services(_,i,n.value.val.state,d.value.value,s).then(v=>{if(v.msg==1){g(ElMessage.error(v.infor)),e.value=[];return}e.value=v.list}))}))(_,i,s)}catch(p){ElMessage.error(p.message)}},d=ref({}),h=ref(null),r=ref(null),u=ref(null),m=_=>{if(n.value.val.state=="jr"){if(_.live_state!="正常"){ElMessage.error(`采集状态为[${_.live_state}]，不为正常 不能进行服务`);return}else if(!_.collect_time){ElMessage.error("未采集人员无法进行服务！请先去采集吧");return}}s.push({name:"collectionView",params:{id:_.id,type:"fw"},query:{state:n.value.val.state,rid:_.rid,name:n.value.val.name}})},f=()=>{clearTimeout(h.value),h.value=setTimeout(async()=>{if(d.value.value!==""){try{const _=await getOldmanList_services(l.value.townSelect,l.value.villageSelect,n.value.val.state,d.value.value,s).then(i=>i);if(_.msg==1){ElMessage.error(_.infor),r.value=null;return}else if(_.hasOwnProperty("errCode")){r.value="";return}r.value=_.list}catch(_){ElMessage.error(_.message),r.value=null,u.value=!0}return}r.value=null},500)};return(_,i)=>(openBlock(),createElementBlock("main",_hoisted_1,[createVNode(_sfc_main$m,{name:"服务",lujing:n.value.val.name},null,8,["lujing"]),createBaseVNode("div",_hoisted_2,[(openBlock(),createBlock(Suspense,null,{fallback:withCtx(()=>[createVNode(_sfc_main$k)]),default:withCtx(()=>[createVNode(_sfc_main$g,{ref_key:"addresscomponent",ref:l,onReturnAddress:c},null,512)]),_:1})),createVNode(_sfc_main$f,{onPreview:m,onOn_input:f,"search-query":d.value,"message-results":r.value},null,8,["search-query","message-results"])]),withDirectives(createBaseVNode("div",_hoisted_3,[createBaseVNode("div",_hoisted_4,[(openBlock(!0),createElementBlock(Fragment,null,renderList(a.value,(p,g)=>(openBlock(),createElementBlock("input",{type:"button",key:g,value:p.name,onClick:v=>o(p),class:normalizeClass((n.value.val.state==p.state?"bg-bottom_bg":" bg-btn_color")+"  rounded-lg m-1 py-2 px-2 text-white")},null,10,_hoisted_5))),128))]),(openBlock(),createBlock(Suspense,null,{fallback:withCtx(()=>[createVNode(_sfc_main$k)]),default:withCtx(()=>[e.value?(openBlock(),createBlock(_sfc_main$i,{key:0,oldmanList:e.value,onClick_check:m},null,8,["oldmanList"])):createCommentVNode("",!0)]),_:1}))],512),[[vShow,!r.value]])]))}},router=createRouter({history:createWebHashHistory("/"),routes:[{path:"/",name:"logins",component:_sfc_main$9},,{path:"/login",name:"login",component:_sfc_main$9},{path:"/weather/:state/:city",name:"cityView",component:_sfc_main$b},{path:"/home",name:"home",component:_sfc_main$e},{path:"/services",name:"services",component:_sfc_main},{path:"/collection/:id/:type",name:"collectionView",component:_sfc_main$1}]}),tailwind="",app=createApp(_sfc_main$n);app.use(router).use();app.mount("#app");
