<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CanJiModel extends Model
{
    use HasFactory;

    protected $table = 'canjiren_info';
    protected $primaryKey = 'ID';
    public $incrementing = false;
    protected $keyType = 'string';
    public $timestamps = false;

    protected $fillable = [
        'ID',
        'CJR_NAME',
        'CJR_IDCARD',
        'CJR_SEX',
        'CJR_BIRTHDAY',
        'CJR_PHONE',
        'CJR_MINZU',
        'CJR_NUMBER',
        'CJR_CATEGORY',
        'CJR_LEVEL',
        'JHR_NAME',
        'JHR_IDCARD',
        'JHR_PHONE',
        'CJR_TOWN',
        'CJR_VILLAGE',
        'CJR_XSYLBXQK',
        'BLR_SIGN',
        'BLR_INFO',
        'DBR_NAME',
        'DBR_PHONE',
        'FILE_IDCARD_FRONT',
        'FILE_IDCARD_BACKGROUND',
        'FILE_HUKOUBEN',
        'FILE_CJR_HEADER',
        'FILE_CJR_MENTOU',
        'FILE_CJR_SHINEI',
        'FILE_CJR_XIANGGUAN',
        'CREATE_TIME',
        'DELETED',
        'CREATE_IP',
        'CREATE_ADDRESS',
        'CREATE_LONGITUDE',
        'CREATE_LATITUDE',
        'AUDIT_STATE',
        'AUDIT_USER',
        'AUDIT_TEXT',
        'AUDIT_TIME',
    ];

    protected $casts = [
        'CJR_BIRTHDAY' => 'date',
        'AUDIT_TIME' => 'date',
        'DELETED' => 'boolean',
        'CREATE_LONGITUDE' => 'double',
        'CREATE_LATITUDE' => 'double',
        'FILE_CJR_XIANGGUAN' => 'json',
        'CJR_CATEGORY' => 'json',
    ];
    public function saveData($request)
    {
        $data = $request->all();
        // 创建一个新的 CanJiModel 实例
        $canji = new CanJiModel();

        // 使用传递的数据填充模型的属性
        foreach ($data as $key => $value) {
            if ($key == "UUID") {
                $key = "ID";
            }
            $canji->{$key} = $value;
        }

        $canji->md5 = md5($canji->ID);
        $current_date_time = date('Y-m-d H:i:s');
        $canji->CREATE_TIME = $current_date_time;
        $canji->CREATE_IP = $request->ip();
        $canji->CREATE_ADDRESS = $request->CREATE_ADDRESS ?: "";
        $canji->CREATE_LONGITUDE = $request->CREATE_LONGITUDE ?: "";
        $canji->CREATE_LATITUDE = $request->CREATE_LATITUDE ?: "";
        // 保存模型实例
        $canji->save();

        // 返回保存后的模型实例
        return $canji;
    }
}
