<?php
namespace App\Http\Controllers;

use App\Http\Controllers\SaveInfoController;
use App\Models\SqlModel;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class GetInfoController extends Controller
{

    //获取sys配置
        public function getSysConf(Request $request)
        {
            //$start_time = microtime(true);
            $validator = Validator::make($request->all(), [
                'option_key' => 'required',
                'data_type'  => 'required',
            ], [
                'option_key.required' => "参数缺失a",
                'data_type.required'  => "参数缺失d",
            ]);
            if ($validator->fails()) {
                $res['msg']   = 1;
                $res['infor'] = $validator->errors()->first();
                return $res;
            }

            $db = Cache::rememberForever('stuffTable-' . $request->option_key, function () use ($request) {
                return DB::table('lk_system_config')
                    ->whereIn('option_key', explode(",", $request->option_key))
                    ->orWhere('option_key', '=', $request->option_key)
                    ->get();
            });

            if (! $db) {
                $res['msg']   = 1;
                $res['infor'] = "获取配置失败！";
            } else {
                $res['msg']   = 2;
                $res['infor'] = "获取配置成功";
                $res['list']  = Auth::jiangwei($db, 'option_key');
            }
            // $end_time = microtime(true);
            // $execution_time = $end_time - $start_time;
            // dd($execution_time);
            return $res;
        }
    //获取sys配置
    public function getPoi(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'xy' => 'required',
        ], [
            'xy.required' => "参数缺失a",
        ]);
        if ($validator->fails()) {
            $res['msg']   = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }
        $client  = new Client();
        $url     = 'http://tool.rights.st1019.cn:89';
        $headers = [
            'Referer' => 'https://fw.dongp.xrylfw.com/',
            'Origin'  => 'https://fw.dongp.xrylfw.com',
        ];
        $params = [
            'form_params' => [
                'xy' => $request->xy,
            ],
        ];
        $response = $client->post($url, array_merge($params, ['headers' => $headers]));
        //echo $response->getBody();

        return $response->getBody();
    }
    //获取本周服务次数
    public function getServiceCount_ByWeek(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'isGroup' => 'required',
        ], [
            'isGroup.required' => "参数缺失a",
        ]);
        if ($validator->fails()) {
            $res['msg']   = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        if ($request->isGroup) {
            $res = DB::select("SELECT
             date_table.date AS DATE,
             COUNT(lk_service_orders.start_time) AS service_count
           FROM (
             SELECT
               DATE(DATE_SUB(NOW(), INTERVAL WEEKDAY(NOW()) DAY) + INTERVAL n DAY) AS DATE
             FROM (
               SELECT a.N + (10 * b.N) + (100 * c.N) AS n
               FROM (SELECT 0 AS N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS a
               CROSS JOIN (SELECT 0 AS N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS b
               CROSS JOIN (SELECT 0 AS N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS c
               WHERE a.N + (10 * b.N) + (100 * c.N) < 7
             ) AS days
           ) AS date_table
           LEFT JOIN lk_service_orders
             ON date_table.date = DATE(FROM_UNIXTIME(lk_service_orders.start_time))
             AND lk_service_orders.qd_uid in(SELECT id FROM `lk_service_user` WHERE area=? AND deleted=0)
             AND lk_service_orders.end_time > 0
           GROUP BY date_table.date
           ORDER BY date_table.date ASC", [Auth::guard('qian')->user()->area]);
        } else {
            $res = DB::select("SELECT
             date_table.date AS DATE,
             COUNT(lk_service_orders.start_time) AS service_count
           FROM (
             SELECT
               DATE(DATE_SUB(NOW(), INTERVAL WEEKDAY(NOW()) DAY) + INTERVAL n DAY) AS DATE
             FROM (
               SELECT a.N + (10 * b.N) + (100 * c.N) AS n
               FROM (SELECT 0 AS N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS a
               CROSS JOIN (SELECT 0 AS N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS b
               CROSS JOIN (SELECT 0 AS N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) AS c
               WHERE a.N + (10 * b.N) + (100 * c.N) < 7
             ) AS days
           ) AS date_table
           LEFT JOIN lk_service_orders
             ON date_table.date = DATE(FROM_UNIXTIME(lk_service_orders.start_time))
             AND lk_service_orders.qd_uid = ?
             AND lk_service_orders.end_time > 0
           GROUP BY date_table.date
           ORDER BY date_table.date ASC", [Auth::guard('qian')->user()->id]);
        }
        return $res;
    }
    //获取本年服务次数
    public function getServiceCount_ByYear()
    {
        $res = DB::select("SELECT
         months.month AS MONTH,
         IFNULL(service_count, 0) AS service_count
       FROM
         (SELECT 1 AS MONTH
          UNION SELECT 2
          UNION SELECT 3
          UNION SELECT 4
          UNION SELECT 5
          UNION SELECT 6
          UNION SELECT 7
          UNION SELECT 8
          UNION SELECT 9
          UNION SELECT 10
          UNION SELECT 11
          UNION SELECT 12) AS months
       LEFT JOIN
         (SELECT
            DATE_FORMAT(FROM_UNIXTIME(start_time), '%m') AS MONTH,
            COUNT(*) AS service_count
          FROM
            lk_service_orders
          WHERE
            qd_uid = ?
            AND end_time > 0
            AND YEAR(FROM_UNIXTIME(start_time)) = YEAR(NOW())
          GROUP BY
            DATE_FORMAT(FROM_UNIXTIME(start_time), '%m')
         ) AS service_counts
         ON months.month = service_counts.month
       GROUP BY
         months.month
       ORDER BY
         months.month", [Auth::guard('qian')->user()->id]);
        //dd(Auth::guard('qian')->user()->id);
        return $res;
    }
    //获取本周服务次数
    public function getServiceCount_ByGroup(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'isGroup' => 'required',
        ], [
            'isGroup.required' => "参数缺失a",
        ]);
        if ($validator->fails()) {
            $res['msg']   = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        if ($request->isGroup) {
            $res = DB::select(
                "SELECT
                u.phone,
                CASE u.admin_flag
                WHEN 1 THEN '组长'
                ELSE '成员'
                END flag,
                u.truename,
                ors.c,
                CONCAT(IFNULL(FLOOR(ors.totalmin), 0), '分钟') totalmin
                FROM lk_service_user u
                LEFT JOIN (
                SELECT COUNT(id) AS c,qd_uid, SUM(end_time - start_time) AS totalmin
                FROM lk_service_orders
                WHERE start_time >= ? AND end_time > 0
                GROUP BY qd_uid
                ) ors ON u.id = ors.qd_uid
                WHERE u.ent_id = ?
                GROUP BY u.id
                ORDER BY c DESC;",
                [strtotime(date("Y") . "-" . date("m") . "-1"), Auth::guard('qian')->user()->area]
            );
        }
        return $res;
    }

    //获取town
    public function getTownList(Request $request)
    {
        $t = "=" . Auth::guard('qian')->user()->area . "";
        // if (Auth::guard('qian')->user()->admin_flag == 2) {
        //     $t = "in (select id from lk_service_area where grid=" . Auth::guard('qian')->user()->id . ")";
        // }
        $db = Cache::remember('stuffTable-' . Auth::guard('qian')->user()->ltown . '-' . Auth::guard('qian')->user()->id . '-' . Auth::guard('qian')->user()->area, 60, function () use ($t) {
            return DB::table('lk_service_oldman')
                ->selectRaw("CONCAT(REPLACE(REPLACE(REPLACE(town,'乡',''),'镇',''),'街道',''),'-',COUNT(1),'人') AS label ,CONCAT(REPLACE(REPLACE(REPLACE(town,'乡',''),'镇',''),'街道',''),'-',COUNT(1),'人') AS name,CONCAT(REPLACE(REPLACE(REPLACE(town,'乡',''),'镇',''),'街道',''),'-',COUNT(1),'人') AS value,'town' as type")
            //->whereRaw('area ' . $t)
                ->where('live_state', '!=', '取消资格')
                ->groupBy('town')
                ->orderByRaw("CASE WHEN town LIKE '%" . Auth::guard('qian')->user()->ltown . "%' THEN 1 ELSE 2 END")
                ->get();
        });

        if (! $db) {
            $res['msg']   = 1;
            $res['infor'] = "获取配置失败！";
        } else {
            $res['msg']   = 2;
            $res['infor'] = "获取区域成功";
            $res['list']  = $db;
        }
        return $res;
    }

    //获取village
    public function getVillageList(Request $request)
    {
        $town_name = explode("-", $request->all()['town'])[0];
        $db        = Cache::remember('stuffTable-' . $town_name . '-' . Auth::guard('qian')->user()->area . '-' . Auth::guard('qian')->user()->lbigv, 60, function () use ($town_name) {
            return DB::select(
                "select *,Z.name as label,Z.name as value from(SELECT * FROM(SELECT CONCAT(big_village,'-',COUNT(1),'人') AS name,'village' as type,uuid() as redisKey  FROM
            (SELECT AREA,live_state,town,big_village  FROM lk_service_oldman
                where big_village is not null  and town like ? and live_state!='取消资格' ) AS a
                GROUP BY big_village )AS X)as Z
                ORDER BY CASE WHEN name LIKE '%" . Auth::guard('qian')->user()->lbigv . "%' THEN 1 ELSE 2 END",
                [$town_name . '%']
            );
        });

        if (! $db) {
            $res['msg']   = 1;
            $res['infor'] = "获取配置失败！";
        } else {
            $res['msg']   = 2;
            $res['infor'] = "获取区域成功";
            $res['list']  = $db;
        }
        return $res;
    }
    //采集功能--根据town和village获取oldmanList
    public function getOldmanList_collection(Request $request)
    {
        if (isset($request->oldman_name)) {
            $validator = Validator::make($request->all(), [
                'oldman_name' => 'required|regex:/\p{Han}/u|max:4',
            ], [
                'oldman_name.regex'    => "您输入的待服务人员姓名格式不对！",
                'oldman_name.required' => "您输入的待服务人员姓名不能为空！",
                'oldman_name.max'      => "您输入的待服务人员姓名长度不得超过4个字符！",
            ]);
            if ($validator->fails()) {
                $res['msg']   = 1;
                $res['infor'] = $validator->errors()->first();
                return $res;
            }
            $oldman_name = $request->oldman_name;
            $sql_res     = join("%", mb_str_split($oldman_name));
            $db          = DB::select(
                "SELECT odm.addressinfo,'fs' as tp,odm.newlatitude,odm.newlongitude,odm.name,odm.address,odm.self_phone,odm.live_state,odm.id,odm.cj,FROM_UNIXTIME(odm.collect_time)collect_time,
                case when LOCATE('http',fi.path)>0 then fi.path else concat(?,fi.path) end AS zp
                FROM `lk_service_orders` ors
                left join `lk_service_oldman` odm ON ors.oldman_id=odm.id
                LEFT JOIN `lk_service_files` fi ON odm.id=fi.`father_id` AND fi.f_type='zp' AND fi.tablename='lk_service_oldman'
                WHERE odm.name like ? AND odm.del_flag=0 and ors.dept_id=? GROUP BY odm.id DESC order by odm.name asc",
                [env('APP_URL'), '%' . $sql_res . '%', Auth::guard('qian')->user()->dept_id]
            );
        } else {
            $validator = Validator::make($request->all(), [
                'town'    => 'required',
                'village' => 'required',
            ], [
                'town.required'    => "请选择乡镇！",
                'village.required' => "请选择行政村！",
            ]);
            if ($validator->fails()) {
                $res['msg']   = 1;
                $res['infor'] = $validator->errors()->first();
                return $res;
            }
            $town_name    = explode("-", $request->town)[0];
            $village_name = explode("-", $request->village)[0];
            //DB::connection()->enableQueryLog();
            $db = DB::select(
                "SELECT odm.addressinfo,odm.newlatitude,odm.newlongitude,'fs' as tp,odm.name,odm.address,odm.self_phone,odm.live_state,odm.id,odm.cj,FROM_UNIXTIME(odm.collect_time)collect_time,
                case when LOCATE('http',fi.path)>0 then fi.path else concat(?,fi.path) end AS zp
                FROM `lk_service_oldman` odm
                LEFT JOIN `lk_service_files` fi ON odm.id=fi.`father_id` AND fi.f_type='zp' AND fi.tablename='lk_service_oldman'
                WHERE odm.town LIKE ? AND odm.big_village LIKE ? AND odm.live_state!='取消资格' GROUP BY odm.id DESC

                      ORDER BY NAME ASC",
                [env('APP_URL'), $town_name . '%', $village_name . '%']
            );
            //dd(DB::getQueryLog());
        }
        if (! $db) {
            $res['msg']   = 1;
            $res['infor'] = "列表数据为空！";
        } else {
            if (! isset($request->all()['oldman_name'])) {
                DB::table('lk_service_user')
                    ->where('id', Auth::guard('qian')->user()->id)
                    ->update(['lbigv' => $village_name, 'ltown' => $town_name]);
            }

            $res['msg']   = 2;
            $res['infor'] = "获取区域成功";
            $res['list']  = $db;
        }
        return $res;
    }

    //采集功能--根据人员ID获取oldmanInfo
    public function getOldmanInfo_collection(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id'   => [
                "integer", "min:1", "required",
            ],
            'tp'   => [
                "required", Rule::in(['zy', 'fs']),
            ],
            'type' => [
                "required", Rule::in(['fw', 'cj']),
            ],
        ], [
            'id.required' => "参数缺失u",
            'id.integer'  => "参数错误i",
            'id.min'      => "参数错误i",
            'tp.required' => "缺少参数t",
            'tp.in'       => "参数错误t",
            'type.in'     => "参数错误tp",
        ]);
        if ($validator->fails()) {
            $res['msg']   = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        // $validator = Validator::make($request->all(), [
        //     'id' => [
        //         Rule::exists('lk_service_oldman', 'id')->where(function ($query) use ($request) {
        //             return $query->where('qd_uid', '=', Auth::guard('qian')->user()->id);
        //         }),
        //     ],
        // ], [
        //     'id.exists' => "待服务人员不存在/不属于您的小组",
        // ]);
        if ($validator->fails()) {
            $res['msg']   = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        $db = DB::select(
            "SELECT odm.man_state_str,odm.id,odm.name,odm.`address`,odm.`idcard`,odm.`self_phone`,odm.`village`,
                odm.relation_name,odm.`relation_phone`,odm.`cadre_name`,odm.`cadre_phone`,
                odm.`man_state`,odm.`addressinfo`,odm.`newlongitude`,odm.`newlatitude`,
                odm.`addressinfo`,odm.`live_state`,odm.cj,FROM_UNIXTIME(odm.collect_time)collect_time
                FROM `lk_service_oldman` odm
                WHERE odm.`id`=?",
            [$request->id]
        );
        $res_dd = SqlModel::selectCjFromOldmanId($db[0]->id);

        $res['msg']         = 2;
        $res['infor']       = "获取人员信息成功";
        $res['list']        = $db[0];
        $res['list']->image = Auth::jiangwei($res_dd, "f_type") ? Auth::jiangwei($res_dd, "f_type") : new def();
        return $res;
    }

    //服务功能--根据服务ID获取OrderInfo
    public function getOrderInfo_services(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => [
                "integer", "min:1", "required",
            ],
            'tp' => [
                "required", Rule::in(['zy', 'fs']),
            ],
        ], [
            'id.required' => "参数缺失u",
            'id.integer'  => "参数错误i",
            'tp.required' => "缺少参数t",
            'tp.in'       => "参数错误t",
        ]);
        if ($validator->fails()) {
            $res['msg']   = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        $validator = Validator::make($request->all(), [
            'id' => [
                Rule::exists('lk_service_orders', 'id')->where(function ($query) {
                    return $query->where('qd_uid', '=', Auth::guard('qian')->user()->id);
                }),
            ],
        ], [
            'id.exists' => "服务订单不存在/不是您签到服务的",
        ]);
        if ($validator->fails()) {
            $res['msg']   = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        $db = DB::select(
            "SELECT ors.item_plane,ors.extra_service,ors.all_uids,odm.man_state,ors.id,FROM_UNIXTIME(ors.start_time)start_time,start_time start_time_timestamp,u.truename uname,
            case when ors.end_time is null then '未签退' else FROM_UNIXTIME(ors.end_time) end end_time,
            case when ors.end_time is null then '未签退' else end_time end end_time_timestamp
            FROM `lk_service_orders` ors
            left join lk_service_oldman odm on odm.id=ors.oldman_id
            left join lk_service_user u on u.id=ors.qd_uid
            WHERE ors.`id`=?",
            [$request->id]
        );
        $res_dd         = SqlModel::selectFwFromOrdersId($db[0]->id);
        $res_order_item = SqlModel::selectOrderItemFromOrdersId($db[0]->item_plane, $db[0]->id);

        // dd($res_order_item);

        $res['msg']        = 2;
        $res['infor']      = "获取服务信息成功";
        $res['list']       = $db[0];
        $res['list']->item = $res_order_item;

        // $img_dd = Auth::jiangweiArr($res_dd, "f_type");
        // dd($img_dd);

        // if ($img_dd) {
        //     foreach ($img_dd as $v) {
        //         foreach ($v as $f) {
        //             if ($f->item == null) {
        //                 $f->item = 0;
        //             }
        //             if ($f->t == null) {
        //                 $f->t = 0;
        //             }

        //             $last[$f->f_type][$f->item][$f->t] = $f;
        //         }
        //     }
        // }
        // foreach ($res_order_item['all_items'] as $item) {
        //     for ($i = 0; $i < 4; $i++) {
        //         $all_item_file['fwzp'][$item->id][$i] = isset($last['fwzp'][$item->id][$i]) ? $last['fwzp'][$item->id][$i] : new image();
        //     }
        // }
        // foreach ($last['fwzp'][0] as $old_it) {
        //     $all_item_file['fwzp'][0][$old_it->t] = $last['fwzp'][0][$old_it->t];
        // }
        // for ($i = 0; $i < 2; $i++) {
        //     $all_item_file['fwsp'][0][$i] = isset($last['fwsp'][0][$i]) ? $last['fwsp'][0][$i] : new image();
        // }

        $img_dd = Auth::jiangweiArr($res_dd, "f_type");
        $last   = [];
        if ($img_dd) {
            foreach ($img_dd as $f_type => $files) {
                foreach ($files as $f) {
                    $f->item ??= 0;
                    $f->t ??= 0;

                    $last[$f_type][$f->item][$f->t] = $f;
                }
            }
        }

        $all_item_file = [
            'fwzp' => [],
            'fwsp' => [],
        ];
        foreach ($res_order_item['all_items'] as $item) {
            if ($item->need_number == 1) {
                $js_on = json_decode($item->str_value);
                if (isset($js_on->value)) {
                    foreach ($js_on->value as $v) {
                        //dd($item);
                        if (isset($item->item_values_str) && $item->item_values_str != "") {
                            $sql_cache = SaveInfoController::getNumberStrValue($item->item_values_str, $v->label);

                            if (isset($sql_cache) && $sql_cache != "") {
                                $cv = $sql_cache;
                                if ($item->menu_type == '隐患提醒') {
                                    //dd($item);
                                    $cv = explode("，", $cv);
                                }
                                $res[$v->value] = $cv;
                            }
                        } else {
                            $cv             = Cache::get('stuffTable-' . $request->id . '-' . $v->value);
                            $res[$v->value] = $cv;
                        }
                    }
                }

            }

            foreach (range(0, count(json_decode($item->need_pic_count)->value) - 1) as $i) {
                $all_item_file['fwzp'][$item->itid][$i] = $last['fwzp'][$item->itid][$i] ?? new image();
            }
        }

        if (isset($last['fwzp'][0])) {
            foreach (array_values($last['fwzp'][0]) as $index => $old_it) {
                $all_item_file['fwzp'][0][$index] = $last['fwzp'][0][$old_it->t];
            }
        }

        foreach (range(0, 1) as $i) {
            $all_item_file['fwsp'][0][$i] = $last['fwsp'][0][$i] ?? new image();
        }

        $res['list']->image = $all_item_file ? $all_item_file : new def();

        if ($request->tp == 'fs') {
            //获取可选的协同人员
            $xt_res = DB::select(
                "SELECT id value,truename label,case id when ? then 'false' else 'true' end disabled
     FROM `lk_service_user` WHERE area=? AND deleted=0 ",
                [Auth::guard('qian')->user()->id, Auth::guard('qian')->user()->area]
            );
            $res['list']->xietong  = $xt_res;
            $res['list']->all_uids = array_map('intval', array_filter(explode(',', $res['list']->all_uids)));
        }

        return $res;
    }

    /**
     * 服务功能--根据town和village和state(jr/zz/fwjl)获取oldmanList
     */
    public function getOldmanList_services(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'state' => 'required',
        ], [
            'state.required' => "参数缺失s",
        ]);
        if ($validator->fails()) {
            $res['msg']   = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        if (isset($request->name)) {
            $oldman_name = $request->name;
        }
        //如果使用模糊搜索功能
        if (isset($oldman_name)) {
            $sql_res = join("%", mb_str_split($oldman_name));
            //'%' . $sql_res . '%'
            //Auth::guard('qian')->user()->area
            $db = DB::select(
                "SELECT  -- 修改点：用 GROUP_CONCAT 拼接多个服务名称
            (
                SELECT GROUP_CONCAT(item.name SEPARATOR ';')
                FROM lk_service_items item
                WHERE FIND_IN_SET(item.id, REPLACE(ors.item_plane, ' ', ''))
            ) AS itname,ors.id,ors.addtime,odm.addressinfo,odm.newlatitude,odm.newlongitude,'fs' as tp,odm.name,odm.address,odm.live_state,odm.id,odm.cj,FROM_UNIXTIME(odm.collect_time)collect_time,fi.path AS zp
                FROM `lk_service_orders` ors
                left join lk_service_oldman odm ON ors.`oldman_id`=odm.id
                LEFT JOIN `lk_service_files` fi ON odm.id=fi.`father_id` AND fi.f_type='zp' AND fi.tablename='lk_service_oldman'
                WHERE odm.name like ? AND odm.del_flag=0 AND ors.del_flag=0 and odm.collect_time is not null and ors.dept_id=? order by odm.name desc",
                ['%' . $sql_res . '%', Auth::guard('qian')->user()->dept_id]
            );
            switch ($request->state) {
                //如果是要查询正在服务中，返回主键是rid
                case 'zz':
                    $db = DB::select(
                        "SELECT *,'fs' as tp,
                        (SELECT COUNT(*) FROM `lk_service_orders` WHERE `oldman_id` = res.id AND MONTH(FROM_UNIXTIME(start_time)) = MONTH(NOW())) AS `services_this_month`,
                        (SELECT SUM(TIMESTAMPDIFF(MINUTE, FROM_UNIXTIME(`start_time`), FROM_UNIXTIME(`end_time`))) FROM `lk_service_orders` WHERE `oldman_id` = res.id AND lk_service_orders.end_time>0 AND MONTH(FROM_UNIXTIME(start_time)) = MONTH(NOW())) AS `total_service_duration_minutes`
                    FROM (
                        SELECT ors.id rid, -- 修改点：用 GROUP_CONCAT 拼接多个服务名称
            (
                SELECT GROUP_CONCAT(item.name SEPARATOR ';')
                FROM lk_service_items item
                WHERE FIND_IN_SET(item.id, REPLACE(ors.item_plane, ' ', ''))
            ) AS itname, odm.name, odm.address, odm.live_state, odm.id, odm.cj, FROM_UNIXTIME(odm.collect_time) collect_time, fi.path AS zp,
                        odm.addressinfo,odm.newlatitude,odm.newlongitude
                        FROM `lk_service_orders` ors
                        LEFT JOIN `lk_service_oldman` odm ON ors.`oldman_id` = odm.id
                        LEFT JOIN `lk_service_files` fi ON odm.id = fi.`father_id` AND fi.f_type = 'zp' AND fi.tablename = 'lk_service_oldman'

                        WHERE ors.start_time >0 AND ors.end_time is null AND ors.qd_uid=? AND odm.del_flag =0 AND ors.del_flag=0 AND ors.state=5 AND odm.name like ?
                    ) res

                    ORDER BY CASE WHEN (res.collect_time IS NULL OR res.live_state != '正常') THEN 1 ELSE 0 END ASC, res.name",
                        [Auth::guard('qian')->user()->id, '%' . $sql_res . '%']
                    );
                    # code...
                    break;
                case 'jr':
                    //今日待本公司服务
                    $db = DB::select(
                        "SELECT *,'fs' as tp,
                        (SELECT COUNT(*) FROM `lk_service_orders` WHERE `oldman_id` = res.id AND MONTH(FROM_UNIXTIME(start_time)) = MONTH(NOW())) AS `services_this_month`,
                        (SELECT SUM(TIMESTAMPDIFF(MINUTE, FROM_UNIXTIME(`start_time`), FROM_UNIXTIME(`end_time`))) FROM `lk_service_orders` WHERE `oldman_id` = res.id AND lk_service_orders.end_time>0 AND MONTH(FROM_UNIXTIME(start_time)) = MONTH(NOW())) AS `total_service_duration_minutes`
                    FROM (
                        SELECT ors.id rid, -- 修改点：用 GROUP_CONCAT 拼接多个服务名称
            (
                SELECT GROUP_CONCAT(item.name SEPARATOR ';')
                FROM lk_service_items item
                WHERE FIND_IN_SET(item.id, REPLACE(ors.item_plane, ' ', ''))
            ) AS itname, odm.name, odm.address, odm.live_state, odm.id, odm.cj, FROM_UNIXTIME(odm.collect_time) collect_time, fi.path AS zp,
                        odm.addressinfo,odm.newlatitude,odm.newlongitude
                        FROM `lk_service_orders` ors
                        LEFT JOIN `lk_service_oldman` odm ON ors.`oldman_id` = odm.id
                        LEFT JOIN `lk_service_files` fi ON odm.id = fi.`father_id` AND fi.f_type = 'zp' AND fi.tablename = 'lk_service_oldman'

                        WHERE ors.start_time is null AND ors.dept_id=? AND odm.del_flag =0 AND ors.del_flag=0 AND ors.state=4 AND odm.name like ?
                    ) res

                    ORDER BY CASE WHEN (res.collect_time IS NULL OR res.live_state != '正常') THEN 1 ELSE 0 END ASC, res.name",
                        [Auth::guard('qian')->user()->dept_id, '%' . $sql_res . '%']
                    );
                    // dd(DB::getQueryLog());
                    break;
                case 'fwjl':
                    $db = DB::select(
                        "SELECT *,'fs' as tp  FROM (SELECT odm.addressinfo, -- 修改点：用 GROUP_CONCAT 拼接多个服务名称
            (
                SELECT GROUP_CONCAT(item.name SEPARATOR ';')
                FROM lk_service_items item
                WHERE FIND_IN_SET(item.id, REPLACE(ors.item_plane, ' ', ''))
            ) AS itname,odm.newlatitude,odm.newlongitude,FROM_UNIXTIME(ors.end_time)end_time,FROM_UNIXTIME(ors.start_time)start_time,ors.id rid,odm.name,odm.address,odm.live_state,odm.id,odm.cj,
                    FROM_UNIXTIME(odm.collect_time)collect_time,fi.path AS zp
                    FROM `lk_service_orders` ors
                    LEFT JOIN `lk_service_oldman` odm ON odm.id=ors.`oldman_id`
                    LEFT JOIN `lk_service_files` fi ON fi.`father_id`=odm.id AND fi.f_type='zp' AND fi.tablename='lk_service_oldman'
                    WHERE ors.`qd_uid`=? AND ors.start_time>0 AND odm.name like ? AND ors.`end_time`>0
                    AND odm.del_flag =0 AND ors.del_flag=0 GROUP BY ors.id DESC)res
                    ORDER BY start_time DESC",
                        [Auth::guard('qian')->user()->id, '%' . $sql_res . '%']
                    );
                    break;

                default:
                    $res['msg']   = 1;
                    $res['infor'] = "未查询到结果";
                    break;
            }
        } else {
            //非模糊搜索，使用town village state 三元素搜索
            switch ($request->state) {
                //如果是要查询正在服务中，返回主键是rid
                case 'zz':
                    //DB::connection()->enableQueryLog();
                    $db = DB::select(
                        "SELECT *,'fs' as tp FROM (SELECT odm.addressinfo,
                        -- 修改点：用 GROUP_CONCAT 拼接多个服务名称
            (
                SELECT GROUP_CONCAT(item.name SEPARATOR ';')
                FROM lk_service_items item
                WHERE FIND_IN_SET(item.id, REPLACE(ors.item_plane, ' ', ''))
            ) AS itname,odm.newlatitude,odm.newlongitude,FROM_UNIXTIME(ors.start_time) start_time,ors.id rid,odm.name,odm.self_phone,odm.address,odm.live_state,odm.id,odm.cj,FROM_UNIXTIME(odm.collect_time)collect_time,fi.path AS zp
                        FROM `lk_service_orders` ors
                        LEFT JOIN `lk_service_oldman` odm ON ors.`oldman_id`=odm.id
                        LEFT JOIN `lk_service_files` fi ON odm.id=fi.`father_id` AND fi.f_type='zp' AND fi.tablename='lk_service_oldman'

                        WHERE  ors.start_time >0 AND ors.end_time is null AND ors.qd_uid=? AND odm.del_flag =0 AND ors.del_flag=0 AND ors.state=5)res
                        ORDER BY name DESC",
                        [
                            Auth::guard('qian')->user()->id,
                        ]
                    );
                    //dd(DB::getQueryLog());
                    # code...
                    // dd(strtotime(date('Y-m-d')));
                    break;
                case 'jr':
                    //DB::connection()->enableQueryLog();
                    // $db = DB::select(
                    //     "SELECT * FROM (SELECT ors.id rid,odm.name,odm.address,odm.live_state,odm.id,odm.cj,FROM_UNIXTIME(odm.collect_time)collect_time,fi.path AS zp
                    //     FROM `lk_service_oldman` odm
                    //     LEFT JOIN `lk_service_files` fi ON odm.id=fi.`father_id` AND fi.f_type='zp' AND fi.tablename='lk_service_oldman'
                    //     LEFT JOIN `lk_service_orders` ors ON ors.`oldman_id`=odm.id AND ors.start_time>?
                    //     WHERE odm.town LIKE ? AND odm.big_village LIKE ? AND odm.live_state!='取消资格' and odm.collect_time is not null AND odm.Auth::guard('qian')->user()->area=? GROUP BY odm.id DESC)res
                    //     WHERE res.rid IS NULL ORDER BY  CASE WHEN (res.collect_time IS NULL OR res.live_state !='正常') THEN 1 ELSE 0 END ASC,res.name",
                    //     [strtotime(date('Y-m-d')), $town_name . '%', $village_name . '%', Auth::guard('qian')->user()->area]
                    // );

                    $db = DB::select(
                        "SELECT *,'fs' as tp,
    (SELECT COUNT(*) FROM `lk_service_orders` WHERE `oldman_id` = res.id AND MONTH(FROM_UNIXTIME(start_time)) = MONTH(NOW())) AS `services_this_month`,
    (SELECT SUM(TIMESTAMPDIFF(MINUTE, FROM_UNIXTIME(`start_time`), FROM_UNIXTIME(`end_time`))) FROM `lk_service_orders` WHERE `oldman_id` = res.id AND lk_service_orders.end_time>0 AND MONTH(FROM_UNIXTIME(start_time)) = MONTH(NOW())) AS `total_service_duration_minutes`
    FROM (
        SELECT
            odm.self_phone,
            -- 修改点：用 GROUP_CONCAT 拼接多个服务名称
            (
                SELECT GROUP_CONCAT(item.name SEPARATOR ';')
                FROM lk_service_items item
                WHERE FIND_IN_SET(item.id, REPLACE(ors.item_plane, ' ', ''))
            ) AS itname,
            ors.id rid,
            ors.create_time,
            odm.name,
            odm.address,
            odm.live_state,
            odm.id,
            odm.cj,
            FROM_UNIXTIME(odm.collect_time) collect_time,
            fi.path AS zp,
            odm.addressinfo,
            odm.newlatitude,
            odm.newlongitude
        FROM `lk_service_orders` ors
        LEFT JOIN `lk_service_oldman` odm ON ors.`oldman_id` = odm.id
        LEFT JOIN `lk_service_files` fi ON odm.id = fi.`father_id` AND fi.f_type = 'zp' AND fi.tablename = 'lk_service_oldman'
        WHERE ors.start_time IS NULL
          AND ors.dept_id = ?
          AND odm.del_flag = 0
          AND ors.del_flag = 0
          AND ors.state = 4
    ) res
    ORDER BY
        CASE WHEN (res.collect_time IS NULL OR res.live_state != '正常') THEN 1 ELSE 0 END ASC,
        res.name",
                        [Auth::guard('qian')->user()->dept_id]
                    );

                    //dd(DB::getQueryLog());
                    break;
                case 'fwjl':
                    //DB::connection()->enableQueryLog();
                    $db = DB::select(
                        "SELECT *
                        ,'fs'as tp,
                        (SELECT COUNT(*) FROM `lk_service_orders` WHERE `oldman_id` = res.id AND YEAR(FROM_UNIXTIME(start_time)) = YEAR(NOW()) AND MONTH(FROM_UNIXTIME(start_time)) = MONTH(NOW())) AS `services_this_month`,
                        (SELECT SUM(TIMESTAMPDIFF(MINUTE, FROM_UNIXTIME(`start_time`), FROM_UNIXTIME(`end_time`))) FROM `lk_service_orders` WHERE `oldman_id` = res.id
                        AND lk_service_orders.end_time>0 AND YEAR(FROM_UNIXTIME(start_time)) = YEAR(NOW()) AND MONTH(FROM_UNIXTIME(start_time)) = MONTH(NOW())) AS `total_service_duration_minutes`
                        FROM (SELECT odm.addressinfo,
                        -- 修改点：用 GROUP_CONCAT 拼接多个服务名称
            (
                SELECT GROUP_CONCAT(item.name SEPARATOR ';')
                FROM lk_service_items item
                WHERE FIND_IN_SET(item.id, REPLACE(ors.item_plane, ' ', ''))
            ) AS itname,odm.newlatitude,odm.newlongitude,floor((ors.end_time-ors.start_time)/60)totalTime,FROM_UNIXTIME(ors.end_time)end_time,FROM_UNIXTIME(ors.start_time)start_time,ors.id rid,odm.name,odm.self_phone,odm.address,odm.live_state,odm.id,odm.cj,
                        FROM_UNIXTIME(odm.collect_time)collect_time,fi.path AS zp
                        FROM `lk_service_orders` ors
                        LEFT JOIN `lk_service_oldman` odm ON odm.id=ors.`oldman_id`
                        LEFT JOIN `lk_service_files` fi ON fi.`father_id`=odm.id AND fi.f_type='zp' AND fi.tablename='lk_service_oldman'
                        WHERE ors.`qd_uid`=? AND ors.start_time>0 AND ors.`end_time`>0
                        AND odm.del_flag =0 AND ors.del_flag=0 GROUP BY ors.id DESC)res

                        ORDER BY start_time DESC",
                        [Auth::guard('qian')->user()->id]
                    );
                                    //dd(DB::getQueryLog());
                    $perPage  = 30; // 每页10条记录
                    $pageData = collect($db)->forPage($request->pages, $perPage);

                    $paginator = new LengthAwarePaginator(
                        $pageData,                  // 当前页的记录
                        count($db),                 // 所有记录总数
                        $perPage,                   // 每页记录数
                        $request->pages,            // 当前页码
                        ['path' => url()->current()]// 分页链接
                    );
                    $db = $paginator->items();
                    //dd($db);
                    break;

                default:
                    $res['msg']   = 1;
                    $res['infor'] = "未查询到结果";
                    break;
            }
        }
        if (! $db) {
            $res['msg']   = 1;
            $res['infor'] = "未查询到结果";
        } else {
            $res['msg']   = 2;
            $res['infor'] = "获取区域成功";
            $res['list']  = $db;
        }
        return $res;
    }
}

class def
{
    public string $label = "";
}
class image
{
    public string $path = "";
}
