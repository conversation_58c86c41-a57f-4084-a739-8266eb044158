<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Ramsey\Uuid\Uuid;

class IdCardOcrModel extends Model
{
    use HasFactory;
    public static $client_id = "uF5oumekPHXd4IMTyijBBb7m";
    public static $client_secret = "41aL7f94i6tZUAFTOv2liYRRiJxHalMc";
    public static $grant_type = "client_credentials";
    /**
     * 获取文件base64编码
     * @param string  $path 文件路径
     * @return string base64编码信息，不带文件头
     */
    public static function getFileBase64Content($path)
    {
        return base64_encode(file_get_contents($path));
    }
    //服务功能--根据服务ID获取OrderInfo
    public static function getAccessToken()
    {
        $db = Cache::remember('idcard_accesstoken_id', 2592000, function () {

            $response = json_decode(Http::withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])
                ->timeout(30)
                ->withoutVerifying()
                ->post('http://aip.baidubce.com/oauth/2.0/token?client_id=' . IdCardOcrModel::$client_id . '&client_secret=' . IdCardOcrModel::$client_secret . '&grant_type=' . IdCardOcrModel::$grant_type, [
                    'client_id' => IdCardOcrModel::$client_id,
                    'client_secret' => IdCardOcrModel::$client_secret,
                    'grant_type' => IdCardOcrModel::$grant_type,
                ])
                ->body());
                // dd(123,$response);
            return $response;
        });

        return $db->access_token;
    }
    //人脸检测，检测出有多少人脸，超过一个的可以直接拒绝。
    public static function ocrIdCard($id_card_side,$base64)
    {
        // dd($id_card_side);
        $response = Http::withHeaders([
            'Content-Type' => 'application/x-www-form-urlencoded',
            'Accept' => 'application/json',
        ])
            ->timeout(30)
            ->withoutVerifying()
            ->withBody(http_build_query([
                'id_card_side' => $id_card_side,
                'image' => ($base64),
                'detect_ps' => 'true',
                'detect_risk' => 'true',
                'detect_quality' => 'true',
                'detect_photo' => 'true',
                'detect_card' => 'true',
                'detect_direction' => 'false'
            ]), 'application/x-www-form-urlencoded')
            ->post('https://aip.baidubce.com/rest/2.0/ocr/v1/idcard?access_token=' . IdCardOcrModel::getAccessToken())
            ->json();

        return $response;
    }
}
