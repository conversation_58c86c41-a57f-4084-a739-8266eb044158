import{c as q}from"./dayjs-28e086c5.js";var L={},A={get exports(){return L},set exports(p){L=p}},R={},D={get exports(){return R},set exports(p){R=p}};(function(p,T){(function(v,d){p.exports=d()})(q,function(){return function(v){var d={};function u(a){if(d[a])return d[a].exports;var l=d[a]={i:a,l:!1,exports:{}};return v[a].call(l.exports,l,l.exports,u),l.l=!0,l.exports}return u.m=v,u.c=d,u.d=function(a,l,m){u.o(a,l)||Object.defineProperty(a,l,{enumerable:!0,get:m})},u.r=function(a){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},u.t=function(a,l){if(1&l&&(a=u(a)),8&l||4&l&&typeof a=="object"&&a&&a.__esModule)return a;var m=Object.create(null);if(u.r(m),Object.defineProperty(m,"default",{enumerable:!0,value:a}),2&l&&typeof a!="string")for(var y in a)u.d(m,y,function(x){return a[x]}.bind(null,y));return m},u.n=function(a){var l=a&&a.__esModule?function(){return a.default}:function(){return a};return u.d(l,"a",l),l},u.o=function(a,l){return Object.prototype.hasOwnProperty.call(a,l)},u.p="",u(u.s=0)}([function(v,d,u){var a;function l(r){return["image/png","image/jpeg","image/gif"].some(e=>e===r)}u.r(d),u.d(d,"canvastoDataURL",function(){return y}),u.d(d,"canvastoFile",function(){return x}),u.d(d,"dataURLtoFile",function(){return j}),u.d(d,"dataURLtoImage",function(){return I}),u.d(d,"downloadFile",function(){return F}),u.d(d,"filetoDataURL",function(){return E}),u.d(d,"imagetoCanvas",function(){return M}),u.d(d,"urltoBlob",function(){return B}),u.d(d,"urltoImage",function(){return C}),u.d(d,"compress",function(){return J}),u.d(d,"compressAccurately",function(){return S}),u.d(d,"EImageType",function(){return a}),function(r){r.PNG="image/png",r.JPEG="image/jpeg",r.GIF="image/gif"}(a||(a={}));var m=function(r,e,n,t){return new(n||(n=Promise))(function(i,s){function f(c){try{g(t.next(c))}catch(o){s(o)}}function h(c){try{g(t.throw(c))}catch(o){s(o)}}function g(c){var o;c.done?i(c.value):(o=c.value,o instanceof n?o:new n(function(w){w(o)})).then(f,h)}g((t=t.apply(r,e||[])).next())})};function y(r,e=.92,n=a.JPEG){return m(this,void 0,void 0,function*(){return l(n)||(n=a.JPEG),r.toDataURL(n,e)})}function x(r,e=.92,n=a.JPEG){return new Promise(t=>r.toBlob(i=>t(i),n,e))}var N=function(r,e,n,t){return new(n||(n=Promise))(function(i,s){function f(c){try{g(t.next(c))}catch(o){s(o)}}function h(c){try{g(t.throw(c))}catch(o){s(o)}}function g(c){var o;c.done?i(c.value):(o=c.value,o instanceof n?o:new n(function(w){w(o)})).then(f,h)}g((t=t.apply(r,e||[])).next())})};function j(r,e){return N(this,void 0,void 0,function*(){const n=r.split(",");let t=n[0].match(/:(.*?);/)[1];const i=atob(n[1]);let s=i.length;const f=new Uint8Array(s);for(;s--;)f[s]=i.charCodeAt(s);return l(e)&&(t=e),new Blob([f],{type:t})})}function I(r){return new Promise((e,n)=>{const t=new Image;t.onload=()=>e(t),t.onerror=()=>n(new Error("dataURLtoImage(): dataURL is illegal")),t.src=r})}function F(r,e){const n=document.createElement("a");n.href=window.URL.createObjectURL(r),n.download=e||Date.now().toString(36),document.body.appendChild(n);const t=document.createEvent("MouseEvents");t.initEvent("click",!1,!1),n.dispatchEvent(t),document.body.removeChild(n)}function E(r){return new Promise(e=>{const n=new FileReader;n.onloadend=t=>e(t.target.result),n.readAsDataURL(r)})}var G=function(r,e,n,t){return new(n||(n=Promise))(function(i,s){function f(c){try{g(t.next(c))}catch(o){s(o)}}function h(c){try{g(t.throw(c))}catch(o){s(o)}}function g(c){var o;c.done?i(c.value):(o=c.value,o instanceof n?o:new n(function(w){w(o)})).then(f,h)}g((t=t.apply(r,e||[])).next())})};function M(r,e={}){return G(this,void 0,void 0,function*(){const n=Object.assign({},e),t=document.createElement("canvas"),i=t.getContext("2d");let s,f;for(const h in n)Object.prototype.hasOwnProperty.call(n,h)&&(n[h]=Number(n[h]));if(n.scale){const h=n.scale>0&&n.scale<10?n.scale:1;f=r.width*h,s=r.height*h}else f=n.width||n.height*r.width/r.height||r.width,s=n.height||n.width*r.height/r.width||r.height;switch([5,6,7,8].some(h=>h===n.orientation)?(t.height=f,t.width=s):(t.height=s,t.width=f),n.orientation){case 3:i.rotate(180*Math.PI/180),i.drawImage(r,-t.width,-t.height,t.width,t.height);break;case 6:i.rotate(90*Math.PI/180),i.drawImage(r,0,-t.width,t.height,t.width);break;case 8:i.rotate(270*Math.PI/180),i.drawImage(r,-t.height,0,t.height,t.width);break;case 2:i.translate(t.width,0),i.scale(-1,1),i.drawImage(r,0,0,t.width,t.height);break;case 4:i.translate(t.width,0),i.scale(-1,1),i.rotate(180*Math.PI/180),i.drawImage(r,-t.width,-t.height,t.width,t.height);break;case 5:i.translate(t.width,0),i.scale(-1,1),i.rotate(90*Math.PI/180),i.drawImage(r,0,-t.width,t.height,t.width);break;case 7:i.translate(t.width,0),i.scale(-1,1),i.rotate(270*Math.PI/180),i.drawImage(r,-t.height,0,t.height,t.width);break;default:i.drawImage(r,0,0,t.width,t.height)}return t})}function B(r){return fetch(r).then(e=>e.blob())}function C(r){return new Promise((e,n)=>{const t=new Image;t.onload=()=>e(t),t.onerror=()=>n(new Error("urltoImage(): Image failed to load, please check the image URL")),t.src=r})}var U=function(r,e,n,t){return new(n||(n=Promise))(function(i,s){function f(c){try{g(t.next(c))}catch(o){s(o)}}function h(c){try{g(t.throw(c))}catch(o){s(o)}}function g(c){var o;c.done?i(c.value):(o=c.value,o instanceof n?o:new n(function(w){w(o)})).then(f,h)}g((t=t.apply(r,e||[])).next())})};function J(r,e={}){return U(this,void 0,void 0,function*(){if(!(r instanceof Blob))throw new Error("compress(): First arg must be a Blob object or a File object.");if(typeof e!="object"&&(e=Object.assign({quality:e})),e.quality=Number(e.quality),Number.isNaN(e.quality))return r;const n=yield E(r);let t=n.split(",")[0].match(/:(.*?);/)[1],i=a.JPEG;l(e.type)&&(i=e.type,t=e.type);const s=yield I(n),f=yield M(s,Object.assign({},e)),h=yield y(f,e.quality,i),g=yield j(h,t);return g.size>r.size?r:g})}function S(r,e={}){return U(this,void 0,void 0,function*(){if(!(r instanceof Blob))throw new Error("compressAccurately(): First arg must be a Blob object or a File object.");if(typeof e!="object"&&(e=Object.assign({size:e})),e.size=Number(e.size),Number.isNaN(e.size)||1024*e.size>r.size)return r;e.accuracy=Number(e.accuracy),(!e.accuracy||e.accuracy<.8||e.accuracy>.99)&&(e.accuracy=.95);const n=e.size*(2-e.accuracy)*1024,t=1024*e.size,i=e.size*e.accuracy*1024,s=yield E(r);let f=s.split(",")[0].match(/:(.*?);/)[1],h=a.JPEG;l(e.type)&&(h=e.type,f=e.type);const g=yield I(s),c=yield M(g,Object.assign({},e));let o,w=.5;const O=[null,null];for(let b=1;b<=7;b++){o=yield y(c,w,h);const P=.75*o.length;if(b===7){(n<P||i>P)&&(o=[o,...O].filter(z=>z).sort((z,_)=>Math.abs(.75*z.length-t)-Math.abs(.75*_.length-t))[0]);break}if(n<P)O[1]=o,w-=Math.pow(.5,b+1);else{if(!(i>P))break;O[0]=o,w+=Math.pow(.5,b+1)}}const k=yield j(o,f);return k.size>r.size?r:k})}}])})})(D);(function(p){p.exports=R})(A);export{L as i};
