<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $table = "lk_service_user";
    protected $fillable = [
        'id',
        'ltown',
        'area',
        'lbigv',
        'username',
        'truename',
        'phone',
    ];
    public function selectFromWhere($username, $password)
    {
        $res = $this->select('lk_service_user.id', 'lk_service_user.ltown', 'lk_service_user.area', 'lk_service_user.lbigv',
        'lk_service_user.username', 'lk_service_user.truename', 'lk_service_user.phone','lk_service_user.dept_id','sys_dept.dept_name')
            ->leftJoin('sys_dept', 'sys_dept.dept_id', '=', 'lk_service_user.dept_id')
            ->where('username', '=', $username)
            ->where('password', '=', md5($password))
            ->where('deleted', '=', 0)
            ->where(function ($query) {
                $query->where('admin_flag', '=', 0)
                    ->orWhere('admin_flag', '=', 1);
            })
            ->first();
        return $res;
    }
    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];
    public static $rules = [
        'username' => 'required',
        'id' => 'required'
    ];
    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
    ];

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }
}
