<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SysDept extends Model
{
    protected $table = 'sys_dept';

    protected $primaryKey = 'dept_id';

    public $timestamps = false;

    protected $fillable = [
        'parent_id',
        'ancestors',
        'dept_name',
        'order_num',
        'leader',
        'phone',
        'email',
        'status',
        'del_flag',
        'create_by',
        'create_time',
        'update_by',
        'update_time',
        'point',
        'lonlat',
        'coordinates',
        'type',
        'introduce',
        'remark',
        'district_ids',
        'item_ids',
    ];

    protected $casts = [
        'create_time' => 'datetime',
        'update_time' => 'datetime',
        'point' => 'array',
        'coordinates' => 'array',
    ];

    public function parent()
    {
        return $this->belongsTo(SysDept::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(SysDept::class, 'parent_id');
    }
}
