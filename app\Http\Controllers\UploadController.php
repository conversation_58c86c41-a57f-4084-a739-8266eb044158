<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Models\Filemodel;
use App\Models\IdCardOcrModel;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

class UploadController extends Controller
{

    public function addWatermark($imagePath, $text)
    {
        $image = Image::make($imagePath);

        $imageWidth = $image->getWidth();
        $watermark = Image::canvas($imageWidth, 200);
        $maxFontSize = $imageWidth / 15;
        $x = 500 * ($imageWidth / 1700);
        $watermark->text($text, $x, 100, function ($font) use ($maxFontSize) {
            $font->file(public_path('fonts/SIMSUN.TTC'));
            $font->size($maxFontSize);
            $font->color('#000');
            $font->align('center');
            $font->valign('center');
        });
        $watermark->text($text, $x + 1, 100, function ($font) use ($maxFontSize) {
            $font->file(public_path('fonts/SIMSUN.TTC'));
            $font->size($maxFontSize);
            $font->color('#ffffff');
            $font->align('center');
            $font->valign('center');
        });
        //$watermark->opacity(50);


        $imageHeight = $image->getHeight();
        $watermarkWidth = $watermark->getWidth();
        $watermarkHeight = $watermark->getHeight();

        $image->insert($watermark, 'bottom-right', $imageWidth - $watermarkWidth - 10, $imageHeight - $watermarkHeight - 10);
        //return $watermark->save($imagePath);
        return $image->save($imagePath);
    }



    //接收图片上传接口
    public function recieveImage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'tp' => [
                "required", Rule::in(['zy', 'fs'])
            ],
            'f_type' => 'required',
        ], [
            'f_type.required' => "参数缺失f",
            'tp.required' => "缺少参数t",
            'tp.in' => "参数错误t",
        ]);
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        #region 验证
        if ($request->f_type == 'fwsp' || $request->f_type == 'fwzp') {
            if ($request->tp == 'zy') {
                $tbname = "pgm_beian_orders";
            } else {
                $tbname = "lk_service_orders";
            }
        } else {
            $tbname = "lk_service_oldman";
        }
        $validator = Validator::make($request->all(), [
            'father_id' => 'exists:' . $tbname . ',id|integer|min:1|required',
            'name' => 'required',
            'item_id' => 'required',
            't' => 'required',
        ], [
            'father_id.required' => "参数缺失a",
            'father_id.integer' => "参数错误b",
            'father_id.exists' => "数据有误，上传失败",
            'father_id.min' => "参数错误c",
            'name.required' => "参数缺失d",
            'item_id.required' => "参数缺失u2",
            't.required' => "参数错误p",

        ]);
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }
        #endregion

        //判断如果是采集，照片被打为合格，则不能修改。
        if (in_array($request->f_type, ["zp", "htk", "dm", "yz", "wn", "jj"])) {
            $dbCount = DB::table("lk_service_oldman")
                ->where("id", $request->father_id)
                ->where("cj", "合格")
                ->count();
            if ($dbCount == 1) {
                $res['msg'] = 1;
                $res['infor'] = "采集合格，不能再次上传采集照片";
                return $res;
            }
        }

        $image = $request->file($request->name);
        //dd($image->getMimeType());
        //dd($image->isValid());
        if (!$request->file() || !$image->isValid() || (!str_contains($image->getMimeType(), "image/") && !str_contains($image->getMimeType(), "video/"))) {
            $res['msg'] = 1;
            $res['infor'] = "未收到文件！";
            return $res;
        }

        $MD5 = md5_file($image);
        $filemodel = new Filemodel();
        $db_res = $filemodel->where('md5', $MD5)->get()->first();
        if ($db_res && Storage::disk('public')->exists($db_res->path)) {
            //如果数据库内有此文件的md5，直接拿出path
            $res['infor'] = "秒传成功！";
            $res['path'] = $db_res->path;
        } else {
            if ($image->extension() == 'mp4') {
                $uploadpath = 'upload/' . date('Ymd');
            } else {
                $uploadpath = 'uploadss/' . date('Ymd');
            }

            $filename = preg_replace('/\s+/', '_', $MD5) . "." . $image->extension();
            $tmp = $image->storeAs($uploadpath, $filename, 'public');

            if ($image->extension() != 'mp4') {
                $this->addWatermark($tmp, "当前时间：" . date('Y-m-d H:i:s'));
            }

            $res['infor'] = "上传成功！";
            $res['path'] = $tmp;
        }

        //DB::connection()->enableQueryLog();
        //删除可能重复的，保障 当前father_id f_type tablename 下的 t item 唯一性，对应一个上传的文件
        $del = $filemodel->where('tablename', '=', $tbname)
            ->where('father_id', '=', $request->father_id)
            ->where('f_type', '=', $request->f_type)
            ->where(function ($query) use ($request) {
                $query->where('t', '=', $request->t)
                    ->where('item', '=', $request->item_id)
                    ->orWhere(function ($query) {
                        $query->where('t', '=', null)
                            ->orWhere('item', '=', null);
                    });
            })
            ->delete();
        //dd(DB::getQueryLog());

        if ($tbname == 'lk_service_orders' && $request->item_id > 0) {
            //准备插入item表

            $res_insert_updata = DB::table("lk_service_orders_items")
                ->updateOrInsert([
                    'order_id' => $request->father_id,
                    'item_id' => $request->item_id,
                    'deleted' => 0,
                ], ['addtime' => time()]);
            //dd($res_insert_updata);
        } else if ($tbname == 'pgm_beian_orders' && $request->item_id > 0) {
            //准备插入item表

            $res_insert_updata = DB::table("pgm_beian_orders_items")
                ->updateOrInsert([
                    'order_id' => $request->father_id,
                    'item_id' => $request->item_id,
                    'deleted' => 0,
                ], ['addtime' => time()]);
            //dd($res_insert_updata);
        }

        $res_sql = $filemodel->insert([
            'tablename' => $tbname,
            'father_id' => $request->father_id,
            'path' => $res['path'],
            'f_type' => $request->f_type,
            't' => $request->t,
            'item' => $request->item_id,
            'is_yasuo' => str_contains($image->getMimeType(), "video/") ? '1' : NUll,
            'createby' => Auth::guard('qian')->user()->id,
            'addtime' => time(),

            'md5' => $MD5
        ]);
        if ($res_sql) {
            $res['msg'] = 2;
        } else {
            $res['msg'] = 1;
            $res['infor'] = "上传成功,但是保存失败！";
        }
        return $res;

        //Storage::disk('public')->put($request->name,$request->_file_);
        // //sleep(2);
        // $t = "=" . Auth::guard('qian')->user()->area . "";
        // if (Auth::guard('qian')->user()->admin_flag == 2) {
        //     $t = "in (select id from lk_service_group where grid=" . Auth::guard('qian')->user()->id . ")";
        // }

        // $db = DB::select("SELECT * FROM(SELECT CONCAT(REPLACE(REPLACE(REPLACE(town,'乡',''),'镇',''),'街道',''),'-',COUNT(1),'人') AS name,'town' as type,uuid() as redisKey FROM lk_service_oldman WHERE ent_id {$t} AND live_state!='取消资格'		GROUP BY town )AS X
        // ORDER BY CASE WHEN name LIKE '%" . Auth::guard('qian')->user()->ltown . "%' THEN 1 ELSE 2 END");
        // if (!$db) {
        //     $res['msg'] = 1;
        //     $res['infor'] = "获取配置失败！";
        // } else {
        //     $res['msg'] = 2;
        //     $res['infor'] = "获取区域成功";
        //     //$list['']
        //     $res['list'] = $db;
        // }
        // return $res;
    }
     //接收身份证图片上传接口
     public function recieveIdcardImage(Request $request)
     {
         $image = $request->file('capture_img');
         if (!$request->file() || !$image->isValid() || (!str_contains($image->getMimeType(), "image/") && !str_contains($image->getMimeType(), "video/"))) {
             $res['msg'] = 1;
             $res['infor'] = "未收到文件！";
             return $res;
         }

         $uploadpath = 'uploadImage/' . date('Ymd');
         $MD5 = md5_file($image);
         $filename = preg_replace('/\s+/', '_', $MD5) . "." . $image->extension();
         $tmp = $image->storeAs($uploadpath, $filename, 'public');
        //  Filemodel::create(['path'=>$tmp,'is_use'=>0,'create_time'=>date('Y-m-d H:i:s')]);
         if(isset($request->type) && ($request->type=='idcard_front' || $request->type=='idcard_back')){
             $b64 = base64_encode($image->get());
             $check_ocr=IdCardOcrModel::ocrIdCard('front',$b64);
             $res['ocr']=$check_ocr;
         }
         $res['infor'] = "上传成功！";
         $res['path'] = env('APP_URL').$tmp;
         $res['msg']=2;
         return $res;
     }


    //删除图片接口
    public function delImage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'tp' => [
                "required", Rule::in(['zy', 'fs'])
            ],
        ], [
            'tp.required' => "缺少参数t",
            'tp.in' => "参数错误t",
        ]);
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        #region 验证
        $validator = Validator::make($request->all(), [
            'item_id' => 'required',
            't' => 'required',
            'f_type' => 'required',
            'father_id' => [
                "integer", "min:1", "required"
            ],
        ], [
            'father_id.required' => "参数缺失a",
            'father_id.integer' => "参数错误b",
            'father_id.min' => "参数错误c",
            'item_id.required' => "参数缺失u2",
            't.required' => "参数错误p",
            'f_type.required' => "参数缺失f",
        ]);
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }
        #endregion
        if ($request->f_type == 'fwsp' || $request->f_type == 'fwzp') {
            if ($request->tp == 'zy') {
                $tbname = "pgm_beian_orders";
                $wclos = "uid";
                $wclos_cov = Auth::guard('qian')->user()->id;
            } else {
                $tbname = "lk_service_orders";
                $wclos = "add_uid";
                $wclos_cov = Auth::guard('qian')->user()->id;
            }
        } else {
            $tbname = "lk_service_oldman";
            $wclos = "area";
            $wclos_cov = Auth::guard('qian')->user()->area;
        }

        $filemodel = new Filemodel();
        //查询此文件是否存在file中并且主外键都还在，并且此订单/此待服务人员 属于当前登录者
        //DB::connection()->enableQueryLog();

        $count = $filemodel
            ->select("lk_service_files.*")
            ->leftJoin($tbname, "{$tbname}.id", "=", "lk_service_files.father_id")
            ->where('lk_service_files.item', '=', $request->item_id)
            //滕州->where('lk_service_files.item', '=', $request->item_id == 0 ? null : $request->item_id)
            ->where('lk_service_files.t', '=', $request->t)
            ->where('lk_service_files.f_type', '=', $request->f_type)
            ->where('lk_service_files.father_id', '=', $request->father_id)
            // ->where("{$tbname}.{$wclos}", '=', $wclos_cov)
            ->get();
        //dd(DB::getQueryLog());
        //dd($count);
        if (count($count) != 1) {
            $res['msg'] = 1;
            $res['infor'] = "删除失败！此文件已经不存在！";
            return $res;
        }

        //判断如果是采集，照片被打为合格，则不能修改。
        if (in_array($request->f_type, ["zp", "htk", "dm", "yz", "wn", "jj0", "jj1", "jt1", "jt2", "jj2", "jj3", "jj4", "jj5", "jj6"])) {
            $dbCount = DB::table("lk_service_oldman")
                ->where("id", $request->father_id)
                ->where("cj", "合格")
                ->count();
            if ($dbCount == 1) {
                $res['msg'] = 1;
                $res['infor'] = "采集合格，不能删除采集照片";
                return $res;
            }
        }

        //存在，是正常的请求，那么删掉文件。
        $db_res = $filemodel->where('id', $count[0]->id)->delete();
        if ($db_res) {
            //判断是否是file中的最后一个了，如果是的话(服务项目没有任何文件了)，那么删掉orders_items，清掉此服务。
            $it_count = $filemodel
                ->where('item', '=', $request->item_id)
                ->where('f_type', '=', $request->f_type)
                ->where('father_id', '=', $request->father_id)
                ->get();
            if (count($it_count) == 0) {
                if ($request->tp == 'zy') {
                    DB::table("pgm_beian_orders_items")
                        ->where("order_id", $request->father_id)
                        ->where("item_id", $request->item_id)
                        ->delete();
                } else {
                    DB::table("lk_service_orders_items")
                        ->where("order_id", $request->father_id)
                        ->where("item_id", $request->item_id)
                        ->delete();
                }
            }
            //如果数据库内另有此文件的md5，则不能删除，因为其他地方要用。如果只有这个地方有此文件，则删除文件，清理空间。
            $md5Count = $filemodel->where('md5', $count[0]->md5)
                ->count();
            if ($md5Count == 0) {
                Storage::disk('public')->delete($count[0]->path);
            }
            $res['infor'] = "删除成功！";
            $res['msg'] = 2;
        } else {
            $res['infor'] = "删除失败！";
            $res['msg'] = 1;
        }

        return $res;
    }
}
