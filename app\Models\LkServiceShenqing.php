<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LkServiceShenqing extends Model
{
    protected $table = 'lk_service_shenqing';

    protected $fillable = [
        'add_uid',
        'district_id',
        'oldman_id',
        'man_state',
        'addtime',
        'idcard',
        'name',
        'operator_name',
        'operator_phone',
        'address',
        'sq_remark',
        'state',
        'state_reason',
        'remark',
        'del_flag',
        'update_by',
        'update_time',
        'lonlat',
        'lonlat_address',
    ];

    protected $casts = [
        'addtime' => 'datetime',
        'update_time' => 'datetime',
    ];
    // 定义访问器
    public function getAddtimeAttribute($value)
    {
        // 格式化日期时间，不带 T
        return \Carbon\Carbon::parse($value)->format('Y-m-d H:i:s');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'add_uid');
    }

    public function district()
    {
        return $this->belongsTo(SysDistrict::class, 'district_id');
    }

    public function oldman()
    {
        return $this->belongsTo(LkServiceOldmanModel::class, 'oldman_id');
    }
    // 定义一对多关系
    public function orders()
    {
        return $this->hasMany(LkServiceOrders::class, 'sqid');
    }
}
