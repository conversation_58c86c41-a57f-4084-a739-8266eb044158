<?php
namespace App\Http\Controllers;

use App\Models\LkItemModel;
use App\Models\LkServiceOldmanModel;
use App\Models\LkServiceOrders;
use App\Models\LkServiceShenqing;
use App\Models\LkServiceShenqingItem;
use App\Rules\Id;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class TianBaoController extends Controller
{
    //
    public function checkTbAuthWithIdcard(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'idcard' => [
                "required", new Id,
            ],
        ], [
            'idcard.required' => "请输入身份证号码！",
        ]);
        if ($validator->fails()) {
            $res['msg']   = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }
        $oldman = LkServiceOldmanModel::where('idcard', $request->idcard)->first();
        if (! $oldman) {
            $res['msg']   = 1;
            $res['infor'] = '此人不存在系统中！';
            return $res;
        }

        $idcard = $request->idcard;
        $info   = LkServiceShenqing::where('idcard', $idcard)
            ->where('del_flag', 0)
            ->whereIn('state', [1])
            ->first();

        if ($info) {
            $res['msg']   = 1;
            $res['infor'] = '此人当前仍有待审核的申请，不可同时多次申请服务！';
            return $res;
        }

        // 根据需要处理查询到的数据
        // $info->CJR_NAME 可以获取残疾人姓名字段的值

        $res['msg']   = 2;
        $res['infor'] = '查询成功！';
        return $res;
    }
    public function getPoi(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'xy' => 'required',
        ], [
            'xy.required' => "参数缺失a",
        ]);
        if ($validator->fails()) {
            $res['msg']   = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }
        $client  = new Client();
        $url     = 'http://tool.rights.st1019.cn:89';
        $headers = [
            'Referer' => 'https://fw.dongp.xrylfw.com/',
            'Origin'  => 'https://fw.dongp.xrylfw.com',
        ];
        $params = [
            'form_params' => [
                'xy' => $request->xy,
            ],
        ];
        $response = $client->post($url, array_merge($params, ['headers' => $headers]));
        //echo $response->getBody();

        return $response->getBody();
    }
    public function getTbListWithIdcard(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'idcard' => [
                "required", new Id,
            ],
        ], [
            'idcard.required' => "请输入身份证号码！",
        ]);
        if ($validator->fails()) {
            $res['msg']   = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        $idcard = $request->idcard;
        $info   = LkServiceShenqing::where('idcard', $idcard)
            ->with(['orders' => function ($query) {
                $query->with('items');
            }])
            ->where('del_flag', 0)

            ->orderBy('addtime', 'desc')
            ->get();

        if (count($info) == 0) {
            $res['msg']   = 1;
            $res['infor'] = '此人没有历史申请服务信息！';
            return $res;
        }

        // 根据需要处理查询到的数据
        // $info->CJR_NAME 可以获取残疾人姓名字段的值

        $res['msg']   = 2;
        $res['infor'] = '查询成功！';
        $res['data']  = $info;
        return $res;
    }
    public function getTbWithId(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => [
                "required",
            ],
        ], [
            'id.required' => "请输入id！",
        ]);
        if ($validator->fails()) {
            $res['msg']   = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        $id   = $request->id;
        $info = LkServiceOrders::where('id', $id)
            ->where('del_flag', 0)
            ->first();

        if (! $info) {
            $res['msg']   = 1;
            $res['infor'] = '信息错误！';
            return $res;
        }
        $oldmanInfo = LkServiceOldmanModel::where('idcard', $info->idcard)->where('del_flag', 0)->first();
        if (! $oldmanInfo) {
            return [
                'msg'   => 1,
                'infor' => '未找到对应的数据！',
            ];
        }

        // 根据需要处理查询到的数据
        // $info->CJR_NAME 可以获取残疾人姓名字段的值

        $res['msg']   = 2;
        $res['infor'] = '查询成功！';
        $res['data']  = $info;
        return $res;
    }
    public function getItems(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'idcard' => [
                "required", new Id,
            ],
        ], [
            'idcard.required' => "请输入身份证号码！",
        ]);
        if ($validator->fails()) {
            $res['msg']   = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }
        $oldmanInfo = LkServiceOldmanModel::where('idcard', $request->idcard)->where('del_flag', 0)->first();
        if (! $oldmanInfo) {
            return [
                'msg'   => 1,
                'infor' => '未找到对应的数据！',
            ];
        }
        // 查询所有未删除的服务项目
        $items = LkItemModel::where('del_flag', 0)->get();

        if ($items->isEmpty()) {
            return [
                'msg'   => 1,
                'infor' => '未找到对应的数据！',

            ];
        }

        // 根据 menu_type 聚合数据
        $aggregatedItems = [];

        foreach ($items as $item) {
            $menuType = $item->menu_type;

            // 如果当前 menu_type 不存在，则创建一个新的聚合项
            if (! isset($aggregatedItems[$menuType])) {
                $aggregatedItems[$menuType] = [
                    'label'   => $menuType,
                    'subItem' => [],
                ];
            }
            if ($item->man_state != 0 && $item->man_state != $oldmanInfo->man_state) {
                continue;
            }

            // 将当前项目添加到对应的 subItem 中
            $aggregatedItems[$menuType]['subItem'][] = [
                'name'         => $item->name,
                'id'           => $item->id,
                'menu_type'    => $item->menu_type,
                'price'        => $item->price,
                'man_state'    => $item->man_state,
                'min_duration' => $item->min_duration,
            ];
        }

        // 将聚合后的数据转换为数组
        $result = array_values($aggregatedItems);

        return [
            'msg'        => 2,
            'infor'      => '查询成功！',
            'data'       => $result,
            'oldmanInfo' => $oldmanInfo,
        ];
    }
    public function saveTbInfo(Request $request)
    {
        /*
		{"itemId":[40],"idcard":"******************","operator_name":"","operator_phone":"1111111","remark":"111122222222"}
		*/
        //echo 'ssssss';die();
        $table_name_add      = 'lk_service_shenqing';
        $table_name_item_add = 'lk_service_shenqing_items';
        if (in_array('40', $request['itemId'])) { //公仆云帮办
            if (isset($request['itemId'][1])) {
                $res['msg']   = 1;
                $res['infor'] = '当选择公仆云帮办时，不可选择其它项目！';
                return $res;
            }
            $table_name_add      = 'lk_service_gpybb';
            $table_name_item_add = false;
        }
        //print_r($request['itemId']);die();
        $regx      = '/(^1\d{10}$|^(0\d{2,3}-?|0\d{2,3} )?[1-9]\d{6}?$)/';
        $validator = Validator::make($request->all(), [
            'idcard'         => [
                "required", new Id,
                Rule::exists('lk_service_oldman', 'idcard')->where(function ($query) use ($request) {
                    return $query->where('del_flag', '0');
                }),
                Rule::unique($table_name_add, 'idcard')->where(function ($query) use ($request) {
                    return $query->where('addtime', '>', date('Y-m-d 00:00:00'))
                        ->where('del_flag', 0);
                }),
            ],
            'itemId'         => [
                'required', 'array', 'min:1',
            ],
            'operator_name'  => [
                'required', "regex:/\p{Han}/u", "max:5", "min:2",
            ],
            'operator_phone' => [
                'required', "regex:{$regx}",
            ],
            'lonlat'         => [
                'nullable', 'string', 'max:50',
            ],
            'lonlat_address' => [
                'nullable', 'string', 'max:100',
            ],
        ], [
            'idcard.required'         => "请输入身份证号码！",
            'idcard.exits'            => "此人不在资格名单内！",
            'idcard.unique'           => "此人今日已提交服务申请！无法再申请了",
            'itemId.*.required'       => '服务项目选择有误！',
            'itemId.required'         => '请选择服务项目！',
            'itemId.array'            => '服务项目选择有误！',
            'itemId.min'              => '请至少选择一个服务项目！',
            'operator_name.required'  => '请输入经办人姓名！',
            'operator_name.regex'     => '经办人姓名格式不正确！',
            'operator_name.max'       => '经办人姓名过长！',
            'operator_name.min'       => '经办人姓名过短！',
            'operator_phone.required' => '请输入经办人电话！',
            'operator_phone.regex'    => '经办人电话格式不正确！',

        ]);
        if ($validator->fails()) {
            $res['msg']   = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        $idcard = $request->idcard;
        $info   = LkServiceOldmanModel::where('idcard', $idcard)
            ->where('del_flag', 0)->first();
        //lonlat的格式本来是4.2065916,51.9916086POIN需要转为(4.2065916 51.9916086)，先判断是否为正经的经纬度字符串，如果不是则不转为point格式，如果是则转为point格式，方便后续查询使用。
        if (isset($request->lonlat) && $request->lonlat) {
            $lonlat = $request->lonlat;
            if (preg_match('/^[-+]?[0-9]*\.?[0-9]+,[-+]?[0-9]*\.?[0-9]+$/', $lonlat)) {
                $lonlat          = str_replace(',', ' ', $lonlat);
                $request->lonlat = "POINT({$lonlat})";
            } else {
                $request->lonlat = '';
            }
        }
        $model = new LkServiceShenqing();
        $model->setTable($table_name_add); // 手动指定表名
        $insert_order_id = $model->insertGetId([
            // 'dept_id' => $info->dept_id,
            // 'add_uid' => Auth::guard('qian')->user()->id,
            'district_id'    => $info->district_id,
            'oldman_id'      => $info->id,
            'man_state'      => $info->man_state,
            'addtime'        => date('Y-m-d H:i:s'),
            'idcard'         => $info->idcard,
            'name'           => $info->name,
            'address'        => $info->address,
            'sq_remark'      => $request->remark,
            'operator_name'  => $request->operator_name,
            'operator_phone' => $request->operator_phone,
            'state'          => 1,
            'lonlat'         => $request->lonlat ?? '',
            'lonlat_address' => $request->lonlat_address ?? '',
            // 'item_plane' => implode(',', $request->itemId),
        ]);

        if (0) {
            $insert_order_id = LkServiceShenqing::insertGetId([
                // 'dept_id' => $info->dept_id,
                // 'add_uid' => Auth::guard('qian')->user()->id,
                'district_id'    => $info->district_id,
                'oldman_id'      => $info->id,
                'man_state'      => $info->man_state,
                'addtime'        => date('Y-m-d H:i:s'),
                'idcard'         => $info->idcard,
                'name'           => $info->name,
                'address'        => $info->address,
                'sq_remark'      => $request->remark,
                'operator_name'  => $request->operator_name,
                'operator_phone' => $request->operator_phone,
                'state'          => 1,
                // 'item_plane' => implode(',', $request->itemId),
            ]);
        }

        if (! $insert_order_id) {

            $res['msg']   = 1;
            $res['infor'] = '提交失败！';
            return $res;
        }

        // 根据需要处理查询到的数据
        // $info->CJR_NAME 可以获取残疾人姓名字段的值
        if ($table_name_item_add) {
            foreach ($request->itemId as $key => $value) {
                LkServiceShenqingItem::insert([
                    'sqid'    => $insert_order_id,
                    'item_id' => $value,
                    'state'   => null,
                ]);
            }
        }

        $res['msg']   = 2;
        $res['infor'] = '需求提交成功！请耐心等待管理人员审核';
        $res['id']    = $insert_order_id;
        return $res;
    }
}
