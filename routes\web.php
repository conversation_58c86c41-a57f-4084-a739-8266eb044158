<?php

use App\Http\Controllers\ApiController;
use App\Http\Controllers\GetInfoController;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\SaveInfoController;
use App\Http\Controllers\TianBaoController;
use App\Http\Controllers\UploadController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('api/getToken', [ApiController::class, 'getToken']);

Route::get('/api/error', function () {
    $list['msg']       = "登录信息已失效，请重新登录";
    $list['errorCode'] = 401;
    $list['infor']     = "登录信息已失效，请重新登录";
    return $list;
})->name("login");

//每个客户端IP每分钟最大请求30次 index路由
//->middleware('throttle:120:1')
Route::prefix('api')->group(function () {
    // Route::any('/wechat', 'WeChatController@serve'); // 注释掉不存在的控制器
    Route::controller(LoginController::class)->prefix('login')->group(function () {
        Route::post('/checkLogin', 'checkLogin')->middleware('login');
        Route::post('/doLogin', 'doLogin');
        Route::post('/logOut', 'logOut');
    });

    // Route::controller(GetInfoController::class)->middleware('login')->group(function () {
    Route::controller(GetInfoController::class)->middleware('login')->prefix('getinfo')->group(function () {
        Route::post('/townList', 'getTownList');

        Route::post('/villageList', 'getVillageList');
        Route::post('/getOldmanList_collection', 'getOldmanList_collection');
        Route::post('/getOldmanInfo_collection', 'getOldmanInfo_collection');
        Route::post('/getOldmanList_services', 'getOldmanList_services');
        // Route::post('/getOldmanInfo_services', 'getOldmanInfo_services');
        Route::post('/getOrderInfo_services', 'getOrderInfo_services');
        Route::post('/getSysConfig', 'getSysConf');
        Route::post('/getServiceCount_ByGroup', 'getServiceCount_ByGroup');
        Route::post('/getServiceCount_ByWeek', 'getServiceCount_ByWeek');
        Route::post('/getServiceCount_ByYear', 'getServiceCount_ByYear');
    });

    Route::controller(UploadController::class)->middleware('login')->group(function () {
        Route::post('/upload/image', 'recieveImage');
        // Route::post('/upload/id_image', 'recieveIdcardImage');
        Route::post('/upload/delimg', 'delImage');
    });
    Route::controller(UploadController::class)->group(function () {
        Route::post('/upload/id_image', 'recieveIdcardImage');
    });
    Route::controller(SaveInfoController::class)->middleware('login')->prefix('saveinfo')->group(function () {
        Route::post('/saveOldmanInfo_collection', 'saveOldmanInfo_collection');
        Route::post('/orderStart_services', 'orderStart_services');
        Route::post('/orderEnd_services', 'orderEnd_services');
        Route::post('/setneednumberstr_cache', 'setNeedNumberStr_Cache');
        Route::post('/orderInvalid_services', 'orderInvalid_services');
    });

    Route::controller(ApiController::class)->group(function () {
        Route::any('/getJson', 'getJson');
    });
    // Route::controller(CanJiController::class)->prefix('canjiren')->group(function () {
    //     Route::post('/select', 'selectByPassword');
    //     Route::post('/saveCanJiInfo', 'saveCanJiInfo');
    //     Route::post('/image', 'recieveImage');
    // });
    Route::controller(TianBaoController::class)->middleware('login')->prefix('tb')->group(function () {
        // Route::post('/checkTbAuthWithIdcard', 'checkTbAuthWithIdcard');

        Route::post('/getTbWithId', 'getTbWithId');
    });

    //无需登录
    Route::controller(TianBaoController::class)->prefix('tb')->group(function () {
        Route::post('/checkTbAuthWithIdcard', 'checkTbAuthWithIdcard');
        Route::post('/getItems', 'getItems');
        Route::post('/saveTbInfo', 'saveTbInfo');
        Route::post('/getTbListWithIdcard', 'getTbListWithIdcard');
        Route::post('/getPoi', 'getPoi');
    });
});
