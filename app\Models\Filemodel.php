<?php

namespace App\Models;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Filemodel extends Model
{
    use HasFactory;
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $table = "lk_service_files";
    protected $fillable = [
        'id',
        'path',
        'md5',
    ];
    public function selectFromWhere($md5)
    {
        $res = $this->select('id', 'path', 'md5')
            ->where('md5', '=', $md5)
            ->first();
        dd(DB::getQueryLog());
    }
}
