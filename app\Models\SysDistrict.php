<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SysDistrict extends Model
{
    protected $table = 'sys_district';

    protected $primaryKey = 'district_id';

    public $timestamps = false;

    protected $fillable = [
        'parent_id',
        'ancestors',
        'level',
        'district_name',
        'district_code',
        'order_num',
        'shortname',
        'description',
        'leader',
        'phone',
        'lonlat',
        'coordinates',
        'status',
        'del_flag',
        'create_by',
        'create_time',
        'update_by',
        'update_time',
        'remark',
        'email',
    ];

    protected $casts = [
        'create_time' => 'datetime',
        'update_time' => 'datetime',
        'coordinates' => 'array',
    ];

    public function parent()
    {
        return $this->belongsTo(SysDistrict::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(SysDistrict::class, 'parent_id');
    }
}
