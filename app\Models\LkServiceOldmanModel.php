<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class LkServiceOldmanModel extends Model
{
    use HasFactory;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table      = 'lk_service_oldman';
    protected $primaryKey = 'ID';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'dept_id',
        'district_id',
        'sn',
        'town',
        'big_village',
        'village',
        'address',
        'name',
        'idcard',
        'sex',
        'age',
        'relation',
        'main_name',
        'main_idcard',
        'self_phone',
        'relation_name',
        'relation_phone',
        'cadre_name',
        'cadre_phone',
        'man_state_str',
        'man_state',
        'live_state',
        'ent_id',
        'company_id',
        'collect_time',
        'newlongitude',
        'newlatitude',
        'oldlongitude',
        'oldlatitude',
        'lonlat',
        'addressinfo',
        'add_time',
        'area',
        'prn1',
        'cj',
        'cjpj',
        'sh_userid',
        'prt1',
        'outbj',
        'cjr',
        'tklb',
        'gyfs',
        'rylb',
        'quyu',
        'now_addr',
        'nowaddr_uptime',
        'addr_uptime',
        'old_addr',
        'up_time',
        'lonlat_per',
        'zjhm',
        'cjzh',
        'cjlb',
        'cjdj',
        'signal_poor',
        'service_type',
        'person_type',
        'remark',
        'del_flag',
        'create_by',
        'create_time',
        'update_by',
        'update_time',
        'jkxxpg',
        'zfxxpg',
        'shztpg',
        'iszd',
        'qdxy',
    ];

    /**
     * 指示是否自动维护时间戳
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * 自定义时间戳字段名
     *
     * @var string
     */
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';
    // 只要正常数据
    protected static function booted()
    {
        static::addGlobalScope('withoutDel', function (Builder $builder) {
            $builder->where('live_state','!=', '取消资格')
            ->where('del_flag', 0);
        });
    }
}
