# 订单无法服务API文档

## API概述

**接口名称**: 订单无法服务处理  
**接口路径**: `POST /api/saveinfo/orderInvalid_services`  
**功能描述**: 当服务人员遇到无法提供服务的情况时，调用此API将订单状态更新为"拒绝服务"状态

## 请求参数

### 请求头
```
Content-Type: application/json
Accept: application/json, text/plain, */*
```

### 请求体参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| id | string/integer | 是 | 订单ID | "2190" |
| invalid_reason | string | 是 | 无法服务的原因，最大255字符 | "精神异常语言或肢体攻击" |
| invalid_xy | string | 是 | 拒绝服务时的坐标位置，格式：经度,纬度 | "51.9916086,4.2065916" |
| tp | string | 是 | 服务类型，可选值：'zy'(住院), 'fs'(分散) | "fs" |
| remark | string | 否 | 备注信息，最大500字符 | "不让我做" |

### 请求示例

```json
{
    "id": "2190",
    "invalid_reason": "精神异常语言或肢体攻击",
    "invalid_xy": "51.9916086,4.2065916",
    "tp": "fs",
    "remark": "不让我做"
}
```

## 响应格式

### 成功响应
```json
{
    "msg": 2,
    "infor": "订单已标记为无法服务",
    "order_id": "2190"
}
```

### 错误响应
```json
{
    "msg": 1,
    "infor": "错误信息描述"
}
```

## 错误码说明

| msg值 | 说明 |
|-------|------|
| 1 | 请求失败，infor字段包含具体错误信息 |
| 2 | 请求成功 |

## 常见错误信息

- "参数缺失：订单ID" - 未提供订单ID
- "参数错误：订单ID格式不正确" - 订单ID格式错误
- "订单不存在或状态不允许拒绝服务" - 订单不存在或状态不是4(已派单待服务)或5(正在服务)
- "请填写无法服务的原因" - 未提供invalid_reason参数
- "无法服务原因不能超过255个字符" - invalid_reason过长
- "定位信息获取失败，请尝试刷新，重新定位" - 未提供invalid_xy参数
- "定位信息格式不正确" - invalid_xy格式不符合要求
- "缺少参数tp" - 未提供tp参数
- "参数错误tp" - tp参数值不在允许范围内
- "此订单不属于您，无法拒绝服务" - 订单已分配给其他服务人员
- "更新失败，请重试" - 数据库更新失败
- "系统错误：..." - 系统异常

## 业务逻辑

1. **参数验证**: 验证所有必填参数的格式和有效性
2. **订单状态检查**: 只允许处理状态为4(已派单待服务)或5(正在服务)的订单
3. **权限检查**: 如果用户已认证，检查订单是否属于当前用户或未分配服务人员
4. **数据更新**: 将订单状态更新为8(拒绝服务)，并更新相关字段

## 数据库更新字段

调用此API后，`lk_service_orders`表中的以下字段会被更新：

| 字段名 | 更新值 | 说明 |
|--------|--------|------|
| state | 8 | 订单状态更新为"拒绝服务" |
| extra_service | invalid_reason参数值 | 无法服务的原因 |
| remark | remark参数值 | 备注信息 |
| end_time | 当前时间戳 | 结束时间 |
| end_xy | invalid_xy参数值 | 拒绝服务时的位置坐标 |
| update_time | 当前时间 | 更新时间 |
| update_by | 当前用户ID | 更新者(仅在用户已认证时) |

## 订单状态说明

| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| 1 | 申请中 | 订单刚创建，等待审核 |
| 2 | 乡镇审核同意，待派单 | 审核通过，等待分配服务人员 |
| 3 | 乡镇审核拒绝，结束流程 | 审核被拒绝 |
| 4 | 已派单待服务 | 已分配服务人员，等待开始服务 |
| 5 | 正在服务 | 服务人员已签到，正在提供服务 |
| 6 | 服务完成待结算 | 服务已完成，等待结算 |
| 7 | 已结算 | 服务费用已结算 |
| 8 | 拒绝服务 | 无法提供服务 |
| 0 | 已作废 | 订单已作废 |

## 使用场景

此API主要用于以下场景：
1. 服务对象精神异常，存在攻击行为
2. 服务对象拒绝接受服务
3. 服务地址无法到达
4. 其他客观原因导致无法提供服务

## 注意事项

1. 此API需要用户登录认证
2. 只能处理状态为4或5的订单
3. 坐标格式必须为"经度,纬度"的形式
4. 无法服务原因和备注都有字符长度限制
5. 调用成功后订单状态不可逆转

## cURL示例

```bash
curl 'http://localhost:5175/api/saveinfo/orderInvalid_services' \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Content-Type: application/json' \
  -H 'Cookie: laravel_session=your_session_token' \
  --data-raw '{
    "id":"2190",
    "invalid_reason":"精神异常语言或肢体攻击",
    "invalid_xy":"51.9916086,4.2065916",
    "tp":"fs",
    "remark":"不让我做"
  }'
```
