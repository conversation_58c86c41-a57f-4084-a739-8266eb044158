<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LkServiceShenqingItem extends Model
{
    protected $table = 'lk_service_shenqing_items';

    protected $fillable = [
        'sqid',
        'item_id',
        'state',
        'shtime',
        'remark',
        'refuse_reason',
        'sh_user_id',
        'order_id',
        'dept_id',
        'del_flag',
        'update_by',
        'update_time',
    ];

    protected $casts = [
        'shtime' => 'datetime',
        'update_time' => 'datetime',
    ];

    public function shUser()
    {
        return $this->belongsTo(User::class, 'sh_user_id');
    }

    public function order()
    {
        return $this->belongsTo(LkServiceOrders::class, 'order_id');
    }

    public function dept()
    {
        return $this->belongsTo(SysDept::class, 'dept_id');
    }
}
