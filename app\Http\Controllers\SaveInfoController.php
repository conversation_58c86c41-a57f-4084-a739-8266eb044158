<?php

namespace App\Http\Controllers;

use App\Models\LkServiceOrders;
use App\Models\SqlModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class img
{
    public string $label = "";
    public string $value = "";
}
class SaveInfoController extends Controller
{

    /**
     * 保存采集的信息
     */
    public function saveOldmanInfo_collection(Request $request)
    {
        //DB::connection()->enableQueryLog();
        $regx = '/(^1\d{10}$|^(0\d{2,3}-?|0\d{2,3} )?[1-9]\d{6}?$)/';
        //$regx=["exclude_if:relation_phone","无","required","regex:{$regx}"];
        //$regx = '/(^1\d{10}$|^(0\d{2,3}-?|0\d{2,3} )?[1-9]\d{6}?$)/';
        $validator = Validator::make($request->all(), [
            'id' => 'exists:lk_service_oldman,id|integer|min:1|required',
            'newlatitude' => 'required',
            'newlongitude' => 'required',
            // 'oldlatitude' => 'required',
            // 'oldlongitude' => 'required',
            'addressinfo' => 'required',
            'relation_name' => 'required|regex:/\p{Han}/u|max:4',
            'relation_phone' => ["exclude_if:relation_phone,无", "required", "regex:{$regx}"],
            'cadre_name' => 'required|regex:/\p{Han}/u|max:4',
            'cadre_phone' => ["exclude_if:cadre_phone,无", "required", "regex:{$regx}"],
            'live_state' => 'required', //此处应该限制 只能属于某一个数组 json内的live_state
            'self_phone' => ["exclude_if:self_phone,无", "required", "regex:{$regx}"],
        ], [
            'id.required' => "参数缺失u",
            'id.integer' => "参数错误u",
            'id.exists' => "待服务人员不存在",
            'newlatitude.required' => "缺少定位信息",
            'newlongitude.required' => "缺少定位信息",
            // 'oldlatitude.required' => "参数错误on",
            // 'oldlongitude.required' => "参数错误on2",
            'addressinfo.required' => "定位信息获取失败，请尝试重新定位",
            'relation_name.required' => "监护人姓名未填写！",
            'relation_name.regex' => "监护人姓名格式不对！",
            'relation_name.max' => "监护人姓名不得超过四个字哦！",
            'relation_phone.required' => "监护人电话未填写！",
            'relation_phone.regex' => "监护人电话格式不对！",
            'cadre_name.required' => "村联系人姓名未填写！",
            'cadre_name.regex' => "村联系人姓名格式不对！",
            'cadre_name.max' => "村联系人姓名不得超过四个字哦！",
            'cadre_phone.required' => "村联系人电话未填写！",
            'cadre_phone.regex' => "村联系人电话格式不对！",
            'live_state.required' => "待服务人员生活状态未填写！",
            'self_phone.required' => "待服务人员电话未填写！",
            'self_phone.regex' => "待服务人员电话格式不对！",
        ]);
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }
        if ($request->live_state == '正常' && isset($request->image)) {
            $cj_image = json_decode(SqlModel::getSysConFromValue("cj_type")->option_value);
            $zp = new img();
            $zp->label = "头像";
            $zp->value = "zp";
            $cj_image->value[] = $zp;
            //dd($cj_image->value);
            foreach ($cj_image->value as $key => $value) {
                //dd($request->image[$value->value]);
                if (!isset($request->image[$value->value]) || !isset($request->image[$value->value]['path'])) {
                    $res['msg'] = 1;
                    $res['infor'] = $value->label . "照片未上传";
                    return $res;
                }
            }
        } else if ($request->live_state == '自愿放弃服务' && isset($request->image)) {
            $cj_image = json_decode(SqlModel::getSysConFromValue("zyfq_zp")->option_value);
            $cj_image = $cj_image->image;
            //dd($cj_image);
            //dd($cj_image->value);
            $c = 0;
            foreach ($cj_image as $key => $value) {
                if ((!isset($request->image[$value->value]) || empty($request->image[$value->value]['path'])) && $c == 0) {
                    $res['msg'] = 1;
                    $res['infor'] = "自愿放弃服务" . $value->label . "文件未上传";
                    return $res;
                } else {
                    $c++;
                }
            }
        }
        $res_info = DB::table('lk_service_oldman')
            ->where('id', $request->id)
            ->update([
                'newlatitude' => $request->newlatitude, 'newlongitude' => $request->newlongitude,
                // 'oldlatitude' => $request->oldlatitude, 'oldlongitude' => $request->oldlongitude,
                'addressinfo' => $request->addressinfo,
                'relation_name' => $request->relation_name,
                'relation_phone' => $request->relation_phone, 'cadre_name' => $request->cadre_name,
                'cadre_phone' => $request->cadre_phone, 'live_state' => $request->live_state,
                'self_phone' => $request->self_phone, 'village' => $request->village,
                'collect_time' => time(),
            ]);
        $res['msg'] = 2;
        $res['infor'] = "更新成功！";
        return $res;
    }

    /**
     * 开始签到
     */
    public function orderStart_services(Request $request)
    {
        $regex = "/((^\d+\.\d+)).??((\d+\.\d+$))/";

        $validator = Validator::make($request->all(), [
            // 'id' => 'exists:lk_service_oldman,id|integer|min:1|required|',
            'id' => [
                "integer", "min:1", "required",
            ],
            'start_xy' => [
                "required", "regex:{$regex}",
            ],
            'tp' => [
                "required", Rule::in(['zy', 'fs']),
            ],
        ], [
            'id.required' => "参数缺失u",
            'id.integer' => "参数错误i",
            'start_xy.required' => "定位信息获取失败，请尝试刷新，重新定位",
            'start_xy.regex' => "定位信息获取有误，请尝试刷新，重新定位",
            'tp.required' => "缺少参数t",
            'tp.in' => "参数错误t",
        ]);
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }
        $validator = Validator::make($request->all(), [
            // 'id' => 'exists:lk_service_oldman,id|integer|min:1|required|',
            'id' => [
                Rule::exists('lk_service_orders', 'id')->where(function ($query) use ($request) {
                    return $query->where('del_flag', 0)
                        ->where('dept_id', '=', Auth::guard('qian')->user()->dept_id)
                        ->whereNull('start_time');
                }),

            ],
        ], [
            'id.exists' => "待服务人员不存在",
            // 'id.unique' => "待服务人员今日已经被服务了！不可重复服务",
        ]);
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        //您有正在进行中的分散供养服务，还未签退，请签退后再进行新的服务

        $db_now = DB::table("lk_service_orders")
            ->where('start_time', '>', 0)
            ->whereNull('end_time')
            ->where('qd_uid', '=', Auth::guard('qian')->user()->id)
            ->where('del_flag', 0)
            ->count();
        if ($db_now > 0) {
            $res['msg'] = 1;
            $res['infor'] = "您有正在进行中的服务，还未签退，请签退后再进行新的服务";
            return $res;
        }

        $data['start_xy'] = $request->start_xy;
        $data['start_time'] = time();
        $data['state'] = 5;
        // $data['oldman_id'] = $request->id;
        $data['qd_uid'] = Auth::guard('qian')->user()->id;

        $db = DB::table("lk_service_orders")
            ->where('id', '=', $request->id)
            ->Update($data);
        // ->insertGetId($data);
        if (intval($db) > 0) {
            $res['msg'] = 2;
            $res['infor'] = "签到成功！开始进行服务";
            $res['rid'] = $request->id;
            return $res;
        }

    }
    /**
     * 结束签到
     */
    public function orderEnd_services(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'tp' => [
                "required", Rule::in(['zy', 'fs']),
            ],
        ], [
            'tp.required' => "缺少参数t",
            'tp.in' => "参数错误t",
        ]);
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        $regex_float = "/((^\d+\.\d+)).??((\d+\.\d+$))/";
        $regex_num = "/^\d+(,\d+)*$/";

        $validator = Validator::make($request->all(), [
            'id' => [
                "integer", "min:1", "required",
                Rule::exists('lk_service_orders', 'id')->where(function ($query) {
                    return $query->where('start_time', '>', 0)
                        ->where('state', 5)
                        ->whereNull('end_time')
                        ->where('qd_uid', '=', Auth::guard('qian')->user()->id);
                }),

            ],
            'end_xy' => [
                "required", "regex:{$regex_float}",
            ],
            /*'all_uids' => [
        "required", "regex:{$regex_num}"
        ],*/
        ], [
            'id.required' => "参数缺失u",
            'id.integer' => "参数错误i",
            'id.exists' => "此正在服务的服务订单不存在",
            'end_xy.required' => "定位信息获取失败，请尝试刷新，重新定位",
            'end_xy.regex' => "定位信息获取有误，请尝试刷新，重新定位",
            /*'all_uids.required' => "未选择协同人员！",
        'all_uids.regex' => "协同人员选择有误",*/
        ]);
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        //根据订单id获取lk_service_orders_items表的item_id，根据获取到的item_id,获取lk_service_items表的min_duration，把所有item_id的min_duration加起来，与服务时长进行比较
        $item_ids = DB::table("lk_service_orders_items")
            ->where('order_id', '=', $request->id)
            ->pluck('item_id');
		$kaishi = DB::table("lk_service_orders")
                        ->where('id', '=', $request->id)
                        ->value('start_time');
        if (count($item_ids) > 0) {
			// 一次性查询所有 lk_service_items 表中的数据
			$service_items = DB::table("lk_service_items")
                        //->whereIn('id', $item_ids)
                        ->pluck('min_duration', 'id');
            $min_duration=0;
            foreach ($item_ids as $item_id) {
				$min_duration += $service_items[$item_id] ?? 0;
            }
			//计算当前服务订单的开始时间和当前时间，如果服务时长小于最小服务时长，则不允许签退
            $now = time();
            if (($now - $kaishi) < $min_duration*60) {
                $res['msg'] = 1;
                $res['infor'] = "服务时长小于最小服务时长，无法签退,还差" . ($min_duration*60 - $now + $kaishi) . "秒";
                return $res;
            }
        } else {
            $res['msg'] = 1;
            $res['infor'] = "此服务订单不存在项目，无法签退";
            return $res;
        }

        //         //判断协同人员 时间是否有重叠
        //         $today = date('Y-m-d');
        // $timestamp = strtotime($today);
        //         $xietongDB=DB::select("SELECT * FROM `lk_service_orders` WHERE (FIND_IN_SET(1003721, all_uids) OR add_uid=1003721) AND
        //         ((start_time>=? AND end_time=0) OR (start_time>=? AND end_time>mykaishi AND end_time<NOW()))",
        //         [1003721,1003721,$timestamp,$timestamp])

        //判断做了几个项目
        $items_count = DB::table("lk_service_orders_items")
            ->leftJoin("lk_service_items", "lk_service_orders_items.item_id", "=", "lk_service_items.id")
            ->where("lk_service_orders_items.order_id", "=", $request->id)
            ->where("lk_service_items.deleted", "=", "0")
            ->where("lk_service_orders_items.deleted", "=", "0")
            ->where("lk_service_items.menu_type", "!=", "隐患提醒")
            ->count();

        $odmInfo = SqlModel::getOldmanInfoFromOrderId($request->id);

        //判断协同人员 时间是否有重叠
        $today = date('Y-m-d');
        $timestamp = strtotime($today);
        $xtArr = explode(",", $request->all_uids);
        if (count($xtArr) > 0) {
            foreach ($xtArr as $key => $value) {
                $xietongDB = DB::select(
                    "SELECT odm.name,(SELECT truename FROM lk_service_user WHERE id=?)etruename,
        ors.id,FROM_UNIXTIME(ors.start_time) st, FROM_UNIXTIME(ors.end_time) et,u.truename
         FROM `lk_service_orders` ors
         LEFT JOIN `lk_service_user` u ON ors.add_uid=u.id
         LEFT JOIN `lk_service_oldman` odm on ors.oldman_id=odm.id
         WHERE (FIND_IN_SET(?, ors.all_uids) OR ors.add_uid=?) AND
            ((ors.start_time>=? AND ors.end_time=0) OR (ors.start_time>=? AND ors.end_time>? AND ors.end_time<NOW()))",
                    [$value, $value, $value, $timestamp, $timestamp, $odmInfo->start_time]
                );
                if ($xietongDB) {
                    $res['msg'] = 1;
                    $res['infor'] = "您选择的服务人员[" . $xietongDB[0]->etruename . "]与另一个服务[服务时间:" . $xietongDB[0]->st . ";待服务人员:" . $xietongDB[0]->name . ";签到人员:" . $xietongDB[0]->truename . "]存在时间交叉，无法签退，请选择正确的协同人员！";
                    return $res;
                }
            }
        }
        $need_count = count(explode(',', LkServiceOrders::where('id', $request->id)->first()->item_plane));
        if ($items_count < $need_count) {
            $res['msg'] = 1;
            $res['infor'] = "您已完成的项目数少于需要完成的项目数，请继续完成此服务！";
            return $res;
        }

        //判断是否有某个项目，need_number必填但是没有填写的
        $v_db = DB::select("SELECT * FROM lk_service_orders_items
LEFT JOIN lk_service_orders
ON lk_service_orders.id = lk_service_orders_items.order_id
LEFT JOIN lk_service_items
ON lk_service_items.id = lk_service_orders_items.item_id
WHERE lk_service_orders.id = ?
AND lk_service_items.need_number = 1", [$request->id]);
        if ($v_db) {
            foreach ($v_db as $key => $value) {
                $item_values_str = $value->item_values_str;
                if (!isset($item_values_str) || $item_values_str == "") {
                    $res['msg'] = 1;
                    $res['infor'] = "[" . $value->name . "]项目的文字内容没有填写！";
                    return $res;
                } else {
                    $str_value = json_decode($value->str_value);
                    foreach ($str_value->value as $s_key => $_value) {
                        if ($_value->label == '血糖') {
                            continue;
                        }
                        //dd($item_values_str);
                        $cx = $this->getNumberStrValue($item_values_str, $_value->label);

                        if (!isset($cx) || $cx == "") {
                            //dd($cx);
                            $res['msg'] = 1;
                            $res['infor'] = "[" . $_value->label . "]项目的文字内容没有填写！";
                            return $res;
                        }
                    }
                }
            }
        } else {
        }
        $data['end_xy'] = $request->end_xy;
        $data['state'] = 6;
        $data['end_time'] = time();
        $data['all_uids'] = $request->all_uids;
        $db = DB::table("lk_service_orders")
            ->where('id', '=', $request->id)
            ->Update($data);
        //dd($db);
        if ($db == 1) {
            $res['msg'] = 2;
            $res['infor'] = "服务签退成功！感谢您的努力付出！";
            $res['rid'] = $db;
            return $res;
        }

    }
    public static function addNumberStr($numberStr, $key, $value)
    {
        // 将字符串转换为数组
        $arr = explode(',', $numberStr);

        $arr[] = "$key:$value";

        // 将数组转换为字符串
        $newNumberStr = implode(',', array_filter($arr));

        return $newNumberStr;
    }

    // 修改
    public static function updateNumberStr($numberStr, $key, $value)
    {
        // 将字符串转换为数组
        $arr = explode(',', $numberStr);

        // 查找要修改的键值对
        foreach ($arr as $index => $item) {
            $pair = explode(':', $item);
            if ($pair[0] === $key) {
                $arr[$index] = "$key:$value";
                break;
            }
        }

        // 将数组转换为字符串
        $newNumberStr = implode(',', $arr);

        return $newNumberStr;
    }

    // 查询
    public static function getNumberStrValue($numberStr, $key)
    {
        // 将字符串转换为数组
        $arr = explode(',', $numberStr);

        // 查找键值对
        foreach ($arr as $index => $item) {
            $pair = explode(':', $item);
            if ($pair[0] === $key) {
                return $pair[1];
            }
        }

        return null;
    }

    /**
     * 缓存--服务过程中输入的文本
     */
    public function setNeedNumberStr_Cache(Request $request)
    {

        $validator = Validator::make($request->all(), [
            // 'id' => 'exists:lk_service_oldman,id|integer|min:1|required|',
            'label' => ["required", "max:20"],
            'rid' => [
                "required", "integer",
                Rule::exists('lk_service_orders', 'id'),
            ],
            'text' => ["required", "max:100"],
            'lb' => ["required", "max:20"],
        ], [
            'label.required' => "参数缺失l",
            'label.max' => "长度超限",
            'rid.required' => "参数缺失r",
            'rid.exists' => "不存在",
            'rid.integer' => "参数错误i",
            'text.required' => "未填写内容",
            'text.max' => "长度超限制",
            'lb.required' => "未填写内容lb",
            'lb.max' => "长度超限制lb",
        ]);
        if ($request->label == 'extra_service' && $request->lb == 'extra_service') {
            //进入服务备注 云保存 模式
            $res_save = DB::table('lk_service_orders')
                ->where('id', $request->rid)
                ->update(['extra_service' => $request->text]);
            if ($res_save) {
                $res['msg'] = 2;
                $res['infor'] = "服务备注保存成功";
                $res['value'] = $request->text;
                return $res;
            } else {
                $res['msg'] = 1;
                $res['infor'] = "服务备注保存失败";
                return $res;
            }
        }
        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }
        $vali_db = DB::select("SELECT * FROM lk_service_items it
        LEFT JOIN lk_service_oldman odm ON odm.`man_state`=it.`man_state` or it.`man_state`=0
        LEFT JOIN lk_service_orders ors ON ors.`oldman_id`=odm.id
        LEFT JOIN lk_service_orders_items oitm ON ors.id=oitm.`order_id` AND it.id=oitm.`item_id`
        WHERE ors.id=? and it.versions=? and it.deleted=0  AND oitm.id IS NOT NULL
        AND JSON_CONTAINS(JSON_EXTRACT(it.str_value, '$.value[*].value'), ?)", [$request->rid, env('VERSIONS'), '"' . $request->label . '"']);

        $new_str = null;
        if (!$vali_db) {
            $res['msg'] = 1;
            $res['infor'] = "云数据保存失败！[您需要先上传服务照片]或[此服务项目不存在此备注需要填写]！";
        } else {
            $db1 = Cache::put('stuffTable-' . $request->rid . '-' . $request->label, $request->text, 86400);
            $orders_items_id = $vali_db[0]->id;
            //原来的item_values_str
            $old_item_values_str = $vali_db[0]->item_values_str;
            if (isset($old_item_values_str) && $old_item_values_str !== '') {
                // 字符串不为空且不为null
                $dangqian_str = $this->getNumberStrValue($old_item_values_str, $request->lb);
                if (isset($dangqian_str) && $dangqian_str !== '') {
                    $new_str = $this->updateNumberStr($old_item_values_str, $request->lb, $request->text);
                } else {
                    $new_str = $this->addNumberStr($old_item_values_str, $request->lb, $request->text);
                }
            } else {
                $new_str = $this->addNumberStr($old_item_values_str, $request->lb, $request->text);
            }

            $data['item_values_str'] = $new_str;
            $db2 = DB::table("lk_service_orders_items")
                ->where('id', '=', $orders_items_id)
                ->Update($data);

            if (!$db1 || !$db2) {
                $res['msg'] = 1;
                $res['infor'] = "云端数据保存失败";
            } else {
                $res['msg'] = 2;
                $res['infor'] = "云端数据保存成功";
                //$list['']
                $res['value'] = $db1;
            }
        }

        return $res;
    }

    /**
     * 订单无法服务处理
     */
    public function orderInvalid_services(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => [
                "integer", "min:1", "required",
                Rule::exists('lk_service_orders', 'id')->where(function ($query) {
                    return $query->whereIn('state', [4, 5]); // 允许已派单待服务(4)或正在服务(5)状态的订单
                }),
            ],
            'invalid_reason' => 'required|string|max:255',
            'invalid_xy' => 'required|regex:/^-?\d+(\.\d+)?,-?\d+(\.\d+)?$/',
            'tp' => [
                "required", Rule::in(['zy', 'fs']),
            ],
            'remark' => 'nullable|string|max:500',
        ], [
            'id.required' => "参数缺失：订单ID",
            'id.integer' => "参数错误：订单ID格式不正确",
            'id.exists' => "订单不存在或订单状态不允许拒绝服务",
            'invalid_reason.required' => "请填写无法服务的原因",
            'invalid_reason.string' => "无法服务原因格式不正确",
            'invalid_reason.max' => "无法服务原因不能超过255个字符",
            'invalid_xy.required' => "定位信息获取失败，请尝试刷新，重新定位",
            'invalid_xy.regex' => "定位信息格式不正确",
            'tp.required' => "缺少参数tp",
            'tp.in' => "参数错误tp",
            'remark.string' => "备注格式不正确",
            'remark.max' => "备注不能超过500个字符",
        ]);

        if ($validator->fails()) {
            $res['msg'] = 1;
            $res['infor'] = $validator->errors()->first();
            return $res;
        }

        try {
            // 如果用户已认证，检查订单是否属于该用户或者可以被该用户处理
            if (Auth::guard('qian')->check()) {
                $order = DB::table('lk_service_orders')
                    ->where('id', $request->id)
                    ->whereIn('state', [4, 5])
                    ->first();

                if (!$order) {
                    $res['msg'] = 1;
                    $res['infor'] = "订单不存在或状态不允许拒绝服务";
                    return $res;
                }

                // 检查订单是否属于当前用户或者未分配服务人员
                if ($order->qd_uid !== null && $order->qd_uid != Auth::guard('qian')->user()->id) {
                    $res['msg'] = 1;
                    $res['infor'] = "此订单不属于您，无法拒绝服务";
                    return $res;
                }
            }

            // 更新订单状态为拒绝服务(8)，并更新相关字段
            $updateData = [
                'state' => 8, // 拒绝服务状态
                'extra_service' => $request->invalid_reason, // 无法服务原因
                'remark' => $request->remark, // 备注
                'end_time' => time(), // 设置结束时间
                'end_xy' => $request->invalid_xy, // 拒绝服务时的位置
                'update_time' => now(), // 更新时间
            ];

            // 如果用户已认证，记录更新者
            if (Auth::guard('qian')->check()) {
                $updateData['update_by'] = Auth::guard('qian')->user()->id;
            }

            $affected = DB::table('lk_service_orders')
                ->where('id', $request->id)
                ->update($updateData);

            if ($affected > 0) {
                $res['msg'] = 2;
                $res['infor'] = "订单已标记为无法服务";
                $res['order_id'] = $request->id;
                return $res;
            } else {
                $res['msg'] = 1;
                $res['infor'] = "更新失败，请重试";
                return $res;
            }
        } catch (\Exception $e) {
            $res['msg'] = 1;
            $res['infor'] = "系统错误：" . $e->getMessage();
            return $res;
        }
    }
}
