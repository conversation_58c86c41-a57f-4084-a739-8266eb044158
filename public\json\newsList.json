{"name": "文章资讯-模拟数据", "author": "<PERSON>", "time": "2020年7月28日", "data": [{"id": 0, "title": "「前端铺子」开源了，前端者必备项目", "tImg": "https://cdn.zhoukaiwen.com/logo.png", "type": "All", "time": "2020年07月28日", "read": "201", "tab": [{"Top": "1", "Hot": "1"}], "author": "<PERSON>", "introduceText": "项目基于vue-uniapp，使用colorUi与uView框架，完美支持微信小程序，包含功能：自定义TabBar与顶部、地图轨迹回放、电子签名、自定义相机/键盘、拍照图片水印、在线答题、证件识别、周边定位查询、文档预览、各种图表、行政区域、海报生成器、视频播放、主题切换、时间轴、瀑布流、排行榜、渐变动画、加载动画、请求封装等～ 方便同行小伙伴参考，项目链接：https://gitee.com/kevin_chou/qdpz", "introduceImg": ["https://zhoukaiwen.com/img/Design/app/FotoJet1.jpg", "https://cdn.zhoukaiwen.com/FotoJet2.png", "https://cdn.zhoukaiwen.com/FotoJet3.png", "https://cdn.zhoukaiwen.com/FotoJet4.png", "https://zhoukaiwen.com/img/Design/app/FotoJet6.jpg", "https://cdn.zhoukaiwen.com/FotoJet5.png"]}, {"id": 1, "title": "「易凯商城」电子商城纯前端H5项目，易上手～", "tImg": "https://zhoukaiwen.com/img/yk/yk_logo.jpeg", "type": "All", "time": "2020年07月28日", "read": "125", "tab": [{"Top": "0", "Hot": "1"}], "author": "<PERSON>", "introduceText": "纯前端H5版本商城，页面清晰易上手、部署简单、H5灵活可广泛应用（可App嵌套/微信公众号/H5端），包含：商品展示、商品分类、分销（暂时移除）购物车、地址管理、售后管理、投诉建议、个人中心等页面，🔝 如帮助到您，麻烦Gitee点个 Star 关注版本更新，笔芯♥️，项目链接：https://gitee.com/kevin_chou/ykShop", "introduceImg": ["https://zhoukaiwen.com/img/yk/p1.jpg", "https://zhoukaiwen.com/img/yk/p2.jpg", "https://zhoukaiwen.com/img/yk/p3.jpg", "https://zhoukaiwen.com/img/yk/p4.jpg", "https://zhoukaiwen.com/img/yk/p5.jpg"]}]}