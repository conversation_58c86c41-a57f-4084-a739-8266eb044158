<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LkItemModel extends Model
{
    use HasFactory;
     /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'lk_service_items';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'sort',
        'name',
        'need_number',
        'number_str',
        'man_state',
        'description',
        'period_nums',
        'company_id',
        'addtime',
        'deleted',
        'price',
        'internal_price',
        'start_time',
        'end_time',
        'versions',
        'menu_type',
        'str_value',
        'need_pic_count',
        'del_flag',
        'remark',
        'create_by',
        'create_time',
        'update_by',
        'update_time',
        'min_duration'
    ];

    /**
     * 指示是否自动维护时间戳
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * 自定义时间戳字段名
     *
     * @var string
     */
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';

    /**
     * 将 JSON 字段转换为数组
     *
     * @var array
     */
    protected $casts = [
        'str_value' => 'array',
        'need_pic_count' => 'array',
    ];
}
